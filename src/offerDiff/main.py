"""
应用主入口
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

import uvicorn
from loguru import logger

from config.settings import settings
from src.offerDiff.offerDiff.app import create_app


def setup_logging():
    """配置日志"""
    logger.remove()  # 移除默认处理器
    
    # 控制台日志
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # 文件日志
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip"
    )


def main():
    """主函数"""
    setup_logging()
    
    logger.info(f"启动 {settings.app_name} v{settings.app_version}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"监听地址: {settings.host}:{settings.port}")
    
    app = create_app()
    
    uvicorn.run(
        app,
        host=settings.host,
        port=settings.port,
        debug=settings.debug,
        reload=settings.debug
    )


if __name__ == "__main__":
    main()
