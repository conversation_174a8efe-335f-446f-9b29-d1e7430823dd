"""
图像质量评估指标工具
"""
import math
from typing import Tuple, List, Optional
import numpy as np
import cv2
from scipy import ndimage
from loguru import logger


def calculate_psnr(img1: np.ndarray, img2: np.ndarray) -> float:
    """
    计算峰值信噪比(PSNR)
    
    Args:
        img1: 第一张图像
        img2: 第二张图像
        
    Returns:
        PSNR值
    """
    try:
        mse = np.mean((img1 - img2) ** 2)
        if mse == 0:
            return float('inf')
        
        max_pixel = 255.0
        psnr = 20 * math.log10(max_pixel / math.sqrt(mse))
        return psnr
        
    except Exception as e:
        logger.error(f"计算PSNR失败: {e}")
        return 0.0


def calculate_ssim(img1: np.ndarray, img2: np.ndarray, window_size: int = 11) -> float:
    """
    计算结构相似性指数(SSIM)
    
    Args:
        img1: 第一张图像
        img2: 第二张图像
        window_size: 窗口大小
        
    Returns:
        SSIM值
    """
    try:
        # 转换为浮点数
        img1 = img1.astype(np.float64)
        img2 = img2.astype(np.float64)
        
        # 常数
        k1 = 0.01
        k2 = 0.03
        L = 255  # 像素值范围
        
        c1 = (k1 * L) ** 2
        c2 = (k2 * L) ** 2
        
        # 计算均值
        mu1 = cv2.GaussianBlur(img1, (window_size, window_size), 1.5)
        mu2 = cv2.GaussianBlur(img2, (window_size, window_size), 1.5)
        
        mu1_sq = mu1 ** 2
        mu2_sq = mu2 ** 2
        mu1_mu2 = mu1 * mu2
        
        # 计算方差和协方差
        sigma1_sq = cv2.GaussianBlur(img1 ** 2, (window_size, window_size), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(img2 ** 2, (window_size, window_size), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(img1 * img2, (window_size, window_size), 1.5) - mu1_mu2
        
        # 计算SSIM
        numerator = (2 * mu1_mu2 + c1) * (2 * sigma12 + c2)
        denominator = (mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2)
        
        ssim_map = numerator / denominator
        return np.mean(ssim_map)
        
    except Exception as e:
        logger.error(f"计算SSIM失败: {e}")
        return 0.0


def calculate_entropy(image: np.ndarray) -> float:
    """
    计算图像熵
    
    Args:
        image: 输入图像
        
    Returns:
        图像熵值
    """
    try:
        # 计算直方图
        hist, _ = np.histogram(image.flatten(), bins=256, range=(0, 256))
        
        # 归一化
        hist = hist / hist.sum()
        
        # 计算熵
        entropy = -np.sum(hist * np.log2(hist + 1e-10))
        return entropy
        
    except Exception as e:
        logger.error(f"计算图像熵失败: {e}")
        return 0.0


def calculate_gradient_magnitude(image: np.ndarray) -> Tuple[float, np.ndarray]:
    """
    计算梯度幅值
    
    Args:
        image: 输入图像
        
    Returns:
        (平均梯度幅值, 梯度图像)
    """
    try:
        # 计算梯度
        grad_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
        
        # 计算梯度幅值
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        mean_gradient = np.mean(gradient_magnitude)
        
        return mean_gradient, gradient_magnitude
        
    except Exception as e:
        logger.error(f"计算梯度幅值失败: {e}")
        return 0.0, np.zeros_like(image)


def detect_edges_canny(image: np.ndarray, low_threshold: int = 50, high_threshold: int = 150) -> np.ndarray:
    """
    使用Canny算法检测边缘
    
    Args:
        image: 输入图像
        low_threshold: 低阈值
        high_threshold: 高阈值
        
    Returns:
        边缘图像
    """
    try:
        edges = cv2.Canny(image, low_threshold, high_threshold)
        return edges
        
    except Exception as e:
        logger.error(f"Canny边缘检测失败: {e}")
        return np.zeros_like(image)


def calculate_focus_measure(image: np.ndarray, method: str = "laplacian") -> float:
    """
    计算焦点测量值
    
    Args:
        image: 输入图像
        method: 测量方法 ("laplacian", "sobel", "variance")
        
    Returns:
        焦点测量值
    """
    try:
        if method == "laplacian":
            # 拉普拉斯方差
            laplacian = cv2.Laplacian(image, cv2.CV_64F)
            return laplacian.var()
            
        elif method == "sobel":
            # Sobel梯度方差
            grad_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
            gradient = np.sqrt(grad_x**2 + grad_y**2)
            return gradient.var()
            
        elif method == "variance":
            # 灰度方差
            return np.var(image)
            
        else:
            logger.warning(f"未知的焦点测量方法: {method}")
            return 0.0
            
    except Exception as e:
        logger.error(f"计算焦点测量值失败: {e}")
        return 0.0


def analyze_histogram(image: np.ndarray) -> dict:
    """
    分析图像直方图
    
    Args:
        image: 输入图像
        
    Returns:
        直方图分析结果
    """
    try:
        # 计算直方图
        hist, bins = np.histogram(image.flatten(), bins=256, range=(0, 256))
        
        # 计算统计信息
        mean_intensity = np.mean(image)
        std_intensity = np.std(image)
        min_intensity = np.min(image)
        max_intensity = np.max(image)
        
        # 计算动态范围
        dynamic_range = max_intensity - min_intensity
        
        # 计算峰值位置
        peak_position = np.argmax(hist)
        
        # 计算直方图熵
        hist_normalized = hist / hist.sum()
        entropy = -np.sum(hist_normalized * np.log2(hist_normalized + 1e-10))
        
        return {
            "mean": float(mean_intensity),
            "std": float(std_intensity),
            "min": int(min_intensity),
            "max": int(max_intensity),
            "dynamic_range": int(dynamic_range),
            "peak_position": int(peak_position),
            "entropy": float(entropy),
            "histogram": hist.tolist()
        }
        
    except Exception as e:
        logger.error(f"直方图分析失败: {e}")
        return {"error": str(e)}


def detect_text_regions(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    检测文本区域
    
    Args:
        image: 输入图像
        
    Returns:
        文本区域边界框列表 [(x, y, w, h), ...]
    """
    try:
        # 形态学操作检测文本区域
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        
        # 梯度操作
        grad_x = cv2.Sobel(image, cv2.CV_8U, 1, 0, ksize=3)
        grad_y = cv2.Sobel(image, cv2.CV_8U, 0, 1, ksize=3)
        gradient = cv2.addWeighted(grad_x, 0.5, grad_y, 0.5, 0)
        
        # 二值化
        _, binary = cv2.threshold(gradient, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 形态学闭运算
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (9, 1))
        closed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤轮廓
        text_regions = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 过滤条件：面积、宽高比等
            area = w * h
            aspect_ratio = w / h if h > 0 else 0
            
            if area > 100 and 0.1 < aspect_ratio < 20:
                text_regions.append((x, y, w, h))
        
        return text_regions
        
    except Exception as e:
        logger.error(f"文本区域检测失败: {e}")
        return []


def calculate_image_complexity(image: np.ndarray) -> dict:
    """
    计算图像复杂度
    
    Args:
        image: 输入图像
        
    Returns:
        复杂度分析结果
    """
    try:
        # 边缘密度
        edges = detect_edges_canny(image)
        edge_density = np.sum(edges > 0) / edges.size
        
        # 纹理复杂度（基于灰度共生矩阵）
        # 简化版本：使用局部方差
        kernel = np.ones((5, 5), np.float32) / 25
        local_mean = cv2.filter2D(image.astype(np.float32), -1, kernel)
        local_variance = cv2.filter2D((image.astype(np.float32) - local_mean)**2, -1, kernel)
        texture_complexity = np.mean(local_variance)
        
        # 频域复杂度
        f_transform = np.fft.fft2(image)
        f_shift = np.fft.fftshift(f_transform)
        magnitude_spectrum = np.log(np.abs(f_shift) + 1)
        frequency_complexity = np.std(magnitude_spectrum)
        
        # 综合复杂度评分
        complexity_score = (
            edge_density * 0.4 + 
            min(texture_complexity / 1000, 1.0) * 0.4 + 
            min(frequency_complexity / 10, 1.0) * 0.2
        ) * 100
        
        return {
            "edge_density": float(edge_density),
            "texture_complexity": float(texture_complexity),
            "frequency_complexity": float(frequency_complexity),
            "complexity_score": float(complexity_score)
        }
        
    except Exception as e:
        logger.error(f"计算图像复杂度失败: {e}")
        return {"error": str(e)}
