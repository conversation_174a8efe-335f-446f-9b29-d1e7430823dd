"""
进度跟踪工具
"""
import time
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from loguru import logger


@dataclass
class StepInfo:
    """步骤信息"""
    name: str
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    status: str = "pending"  # pending, running, completed, failed
    progress: int = 0
    message: str = ""
    error: Optional[str] = None


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        self.task_name = ""
        self.total_steps = 0
        self.current_step = 0
        self.steps: List[StepInfo] = []
        self.start_time = None
        self.end_time = None
        self.callbacks: List[Callable] = []
    
    def start(self, task_name: str, total_steps: int):
        """
        开始任务
        
        Args:
            task_name: 任务名称
            total_steps: 总步骤数
        """
        self.task_name = task_name
        self.total_steps = total_steps
        self.current_step = 0
        self.steps = []
        self.start_time = time.time()
        self.end_time = None
        
        logger.info(f"开始任务: {task_name}, 总步骤: {total_steps}")
    
    def add_step(self, step_name: str) -> int:
        """
        添加步骤
        
        Args:
            step_name: 步骤名称
            
        Returns:
            步骤索引
        """
        step = StepInfo(name=step_name)
        self.steps.append(step)
        return len(self.steps) - 1
    
    def start_step(self, step_name: str) -> int:
        """
        开始步骤
        
        Args:
            step_name: 步骤名称
            
        Returns:
            当前进度百分比
        """
        # 查找或创建步骤
        step_index = None
        for i, step in enumerate(self.steps):
            if step.name == step_name:
                step_index = i
                break
        
        if step_index is None:
            step_index = self.add_step(step_name)
        
        # 更新步骤状态
        step = self.steps[step_index]
        step.start_time = time.time()
        step.status = "running"
        step.progress = 0
        
        # 更新当前步骤
        self.current_step = step_index + 1
        
        # 计算总体进度
        progress = self._calculate_progress()
        
        # 触发回调
        self._trigger_callbacks(progress, f"开始步骤: {step_name}")
        
        logger.debug(f"开始步骤: {step_name} ({self.current_step}/{self.total_steps})")
        return progress
    
    def update_step(self, step_name: str, progress: int = 100, message: str = "") -> int:
        """
        更新步骤进度
        
        Args:
            step_name: 步骤名称
            progress: 步骤进度 (0-100)
            message: 进度消息
            
        Returns:
            当前总体进度百分比
        """
        # 查找步骤
        step = None
        for s in self.steps:
            if s.name == step_name:
                step = s
                break
        
        if step is None:
            logger.warning(f"未找到步骤: {step_name}")
            return self._calculate_progress()
        
        # 更新步骤信息
        step.progress = min(100, max(0, progress))
        step.message = message
        
        if progress >= 100:
            step.status = "completed"
            step.end_time = time.time()
        
        # 计算总体进度
        total_progress = self._calculate_progress()
        
        # 触发回调
        self._trigger_callbacks(total_progress, message or f"更新步骤: {step_name}")
        
        return total_progress
    
    def fail_step(self, step_name: str, error: str) -> int:
        """
        标记步骤失败
        
        Args:
            step_name: 步骤名称
            error: 错误信息
            
        Returns:
            当前总体进度百分比
        """
        # 查找步骤
        step = None
        for s in self.steps:
            if s.name == step_name:
                step = s
                break
        
        if step is None:
            logger.warning(f"未找到步骤: {step_name}")
            return self._calculate_progress()
        
        # 更新步骤状态
        step.status = "failed"
        step.error = error
        step.end_time = time.time()
        
        # 计算总体进度
        total_progress = self._calculate_progress()
        
        # 触发回调
        self._trigger_callbacks(total_progress, f"步骤失败: {step_name}")
        
        logger.error(f"步骤失败: {step_name}, 错误: {error}")
        return total_progress
    
    def complete(self, message: str = "任务完成"):
        """
        完成任务
        
        Args:
            message: 完成消息
        """
        self.end_time = time.time()
        
        # 触发回调
        self._trigger_callbacks(100, message)
        
        # 计算总耗时
        total_time = self.end_time - self.start_time if self.start_time else 0
        
        logger.info(f"任务完成: {self.task_name}, 总耗时: {total_time:.2f}秒")
    
    def _calculate_progress(self) -> int:
        """计算总体进度"""
        if not self.steps or self.total_steps == 0:
            return 0
        
        # 计算已完成步骤的进度
        total_progress = 0
        for step in self.steps:
            if step.status == "completed":
                total_progress += 100
            elif step.status == "running":
                total_progress += step.progress
            # pending 和 failed 步骤不计入进度
        
        # 计算百分比
        max_progress = self.total_steps * 100
        return min(100, int(total_progress / max_progress * 100)) if max_progress > 0 else 0
    
    def get_status(self) -> Dict:
        """
        获取当前状态
        
        Returns:
            状态字典
        """
        current_time = time.time()
        
        status = {
            "task_name": self.task_name,
            "total_steps": self.total_steps,
            "current_step": self.current_step,
            "progress": self._calculate_progress(),
            "start_time": self.start_time,
            "elapsed_time": current_time - self.start_time if self.start_time else 0,
            "estimated_remaining": self._estimate_remaining_time(),
            "steps": []
        }
        
        # 添加步骤信息
        for step in self.steps:
            step_info = {
                "name": step.name,
                "status": step.status,
                "progress": step.progress,
                "message": step.message,
                "start_time": step.start_time,
                "end_time": step.end_time,
                "duration": step.end_time - step.start_time if step.start_time and step.end_time else None,
                "error": step.error
            }
            status["steps"].append(step_info)
        
        return status
    
    def _estimate_remaining_time(self) -> Optional[float]:
        """估算剩余时间"""
        if not self.start_time:
            return None
        
        current_time = time.time()
        elapsed_time = current_time - self.start_time
        progress = self._calculate_progress()
        
        if progress <= 0:
            return None
        
        # 基于当前进度估算总时间
        estimated_total_time = elapsed_time * 100 / progress
        remaining_time = estimated_total_time - elapsed_time
        
        return max(0, remaining_time)
    
    def add_callback(self, callback: Callable):
        """
        添加进度回调函数
        
        Args:
            callback: 回调函数，接收 (progress, message) 参数
        """
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """
        移除进度回调函数
        
        Args:
            callback: 要移除的回调函数
        """
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def _trigger_callbacks(self, progress: int, message: str):
        """触发所有回调函数"""
        for callback in self.callbacks:
            try:
                callback(progress, message)
            except Exception as e:
                logger.error(f"进度回调函数执行失败: {e}")
    
    def get_summary(self) -> Dict:
        """
        获取任务摘要
        
        Returns:
            任务摘要字典
        """
        total_time = (self.end_time or time.time()) - self.start_time if self.start_time else 0
        
        completed_steps = sum(1 for step in self.steps if step.status == "completed")
        failed_steps = sum(1 for step in self.steps if step.status == "failed")
        
        summary = {
            "task_name": self.task_name,
            "total_time": total_time,
            "total_steps": len(self.steps),
            "completed_steps": completed_steps,
            "failed_steps": failed_steps,
            "success_rate": completed_steps / len(self.steps) * 100 if self.steps else 0,
            "average_step_time": total_time / len(self.steps) if self.steps else 0
        }
        
        return summary
