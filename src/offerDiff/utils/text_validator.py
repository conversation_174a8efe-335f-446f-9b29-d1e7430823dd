"""
文本验证工具
"""
import re
import unicodedata
from typing import Dict, List, Tuple, Optional
from loguru import logger


class TextValidator:
    """文本验证器"""
    
    def __init__(self):
        self.setup_validation_rules()
    
    def setup_validation_rules(self):
        """设置验证规则"""
        # 有效字符集
        self.valid_chinese_chars = set()
        self.valid_english_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
        self.valid_digits = set('0123456789')
        self.valid_punctuation = set('.,;:!?()[]{}"\'-/\\@#$%^&*+=<>|`~¥€£')
        
        # 常见汉字范围
        for i in range(0x4e00, 0x9fff):  # CJK统一汉字
            self.valid_chinese_chars.add(chr(i))
        
        # 数字格式验证模式
        self.number_patterns = {
            "integer": re.compile(r'^-?\d+$'),
            "decimal": re.compile(r'^-?\d+\.\d+$'),
            "currency": re.compile(r'^[¥$€£]?\s*-?\d{1,3}(,\d{3})*(\.\d{2})?$'),
            "percentage": re.compile(r'^-?\d+(\.\d+)?%$'),
            "phone": re.compile(r'^1[3-9]\d{9}$'),
            "email": re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
            "date": re.compile(r'^\d{4}[-/]\d{1,2}[-/]\d{1,2}$'),
            "time": re.compile(r'^\d{1,2}:\d{2}(:\d{2})?$')
        }
        
        # 可疑字符（常见OCR错误）
        self.suspicious_chars = {
            '|', '\\', '/', '_', '~', '`', '^', 
            '§', '¶', '†', '‡', '•', '‰', '‱'
        }
        
        # 常见错误模式
        self.error_patterns = [
            re.compile(r'[a-zA-Z]{20,}'),  # 过长的英文字符串
            re.compile(r'\d{15,}'),        # 过长的数字串
            re.compile(r'[^\w\s\u4e00-\u9fff.,;:!?()[\]{}"\'-/\\@#$%^&*+=<>|`~¥€£]{3,}'),  # 连续特殊字符
        ]
    
    def validate_text(self, text: str, text_type: str = "text") -> Dict:
        """
        验证文本
        
        Args:
            text: 待验证的文本
            text_type: 文本类型
            
        Returns:
            验证结果字典
        """
        try:
            if not text:
                return {
                    "is_valid": False,
                    "confidence": 0.0,
                    "issues": ["空文本"],
                    "text_length": 0,
                    "char_stats": {}
                }
            
            # 基础统计
            char_stats = self._analyze_characters(text)
            
            # 类型特定验证
            type_validation = self._validate_by_type(text, text_type)
            
            # 通用验证
            general_validation = self._validate_general(text, char_stats)
            
            # 可疑模式检测
            suspicious_patterns = self._detect_suspicious_patterns(text)
            
            # 综合评估
            overall_validation = self._calculate_overall_validation(
                type_validation, general_validation, suspicious_patterns, char_stats
            )
            
            result = {
                "is_valid": overall_validation["is_valid"],
                "confidence": overall_validation["confidence"],
                "issues": overall_validation["issues"],
                "text_length": len(text),
                "char_stats": char_stats,
                "type_validation": type_validation,
                "general_validation": general_validation,
                "suspicious_patterns": suspicious_patterns
            }
            
            return result
            
        except Exception as e:
            logger.error(f"文本验证失败: {e}")
            return {
                "is_valid": False,
                "confidence": 0.0,
                "issues": [f"验证错误: {str(e)}"],
                "text_length": len(text) if text else 0,
                "char_stats": {}
            }
    
    def _analyze_characters(self, text: str) -> Dict:
        """分析字符组成"""
        try:
            stats = {
                "total_chars": len(text),
                "chinese_chars": 0,
                "english_chars": 0,
                "digits": 0,
                "punctuation": 0,
                "spaces": 0,
                "other_chars": 0,
                "invalid_chars": 0,
                "char_types": set()
            }
            
            for char in text:
                # 统计字符类型
                if char in self.valid_chinese_chars:
                    stats["chinese_chars"] += 1
                    stats["char_types"].add("chinese")
                elif char in self.valid_english_chars:
                    stats["english_chars"] += 1
                    stats["char_types"].add("english")
                elif char in self.valid_digits:
                    stats["digits"] += 1
                    stats["char_types"].add("digit")
                elif char in self.valid_punctuation:
                    stats["punctuation"] += 1
                    stats["char_types"].add("punctuation")
                elif char.isspace():
                    stats["spaces"] += 1
                    stats["char_types"].add("space")
                else:
                    stats["other_chars"] += 1
                    # 检查是否为无效字符
                    if ord(char) < 32 or ord(char) > 126 and char not in self.valid_chinese_chars:
                        stats["invalid_chars"] += 1
            
            # 计算比例
            total = stats["total_chars"]
            if total > 0:
                stats["chinese_ratio"] = stats["chinese_chars"] / total
                stats["english_ratio"] = stats["english_chars"] / total
                stats["digit_ratio"] = stats["digits"] / total
                stats["invalid_ratio"] = stats["invalid_chars"] / total
            
            # 转换set为list以便JSON序列化
            stats["char_types"] = list(stats["char_types"])
            
            return stats
            
        except Exception as e:
            logger.error(f"字符分析失败: {e}")
            return {"total_chars": len(text), "error": str(e)}
    
    def _validate_by_type(self, text: str, text_type: str) -> Dict:
        """根据类型验证文本"""
        try:
            validation = {
                "type": text_type,
                "is_valid": True,
                "confidence": 1.0,
                "issues": []
            }
            
            if text_type in self.number_patterns:
                # 数字类型验证
                pattern = self.number_patterns[text_type]
                if not pattern.match(text.strip()):
                    validation["is_valid"] = False
                    validation["confidence"] = 0.3
                    validation["issues"].append(f"不符合{text_type}格式")
                
            elif text_type == "financial_term":
                # 金融术语验证
                if not self._validate_financial_term(text):
                    validation["is_valid"] = False
                    validation["confidence"] = 0.5
                    validation["issues"].append("不是有效的金融术语")
            
            elif text_type == "text":
                # 普通文本验证
                if len(text.strip()) < 1:
                    validation["is_valid"] = False
                    validation["confidence"] = 0.0
                    validation["issues"].append("文本过短")
                elif len(text) > 1000:
                    validation["confidence"] = 0.8
                    validation["issues"].append("文本过长")
            
            return validation
            
        except Exception as e:
            logger.error(f"类型验证失败: {e}")
            return {
                "type": text_type,
                "is_valid": False,
                "confidence": 0.0,
                "issues": [f"类型验证错误: {str(e)}"]
            }
    
    def _validate_general(self, text: str, char_stats: Dict) -> Dict:
        """通用验证"""
        try:
            validation = {
                "is_valid": True,
                "confidence": 1.0,
                "issues": []
            }
            
            # 检查无效字符比例
            invalid_ratio = char_stats.get("invalid_ratio", 0)
            if invalid_ratio > 0.1:  # 超过10%的无效字符
                validation["is_valid"] = False
                validation["confidence"] = 0.3
                validation["issues"].append("包含过多无效字符")
            elif invalid_ratio > 0.05:  # 超过5%的无效字符
                validation["confidence"] = 0.7
                validation["issues"].append("包含少量无效字符")
            
            # 检查字符组成合理性
            total_chars = char_stats.get("total_chars", 0)
            if total_chars > 0:
                # 检查是否全是数字或标点
                digit_ratio = char_stats.get("digit_ratio", 0)
                punct_ratio = char_stats.get("punctuation", 0) / total_chars
                
                if digit_ratio > 0.9 and total_chars > 20:
                    validation["confidence"] = 0.6
                    validation["issues"].append("可能是错误识别的数字串")
                
                if punct_ratio > 0.5:
                    validation["confidence"] = 0.5
                    validation["issues"].append("标点符号过多")
            
            # 检查文本长度合理性
            if total_chars < 1:
                validation["is_valid"] = False
                validation["confidence"] = 0.0
                validation["issues"].append("文本为空")
            elif total_chars > 500:
                validation["confidence"] = 0.8
                validation["issues"].append("文本过长")
            
            return validation
            
        except Exception as e:
            logger.error(f"通用验证失败: {e}")
            return {
                "is_valid": False,
                "confidence": 0.0,
                "issues": [f"通用验证错误: {str(e)}"]
            }
    
    def _detect_suspicious_patterns(self, text: str) -> Dict:
        """检测可疑模式"""
        try:
            suspicious = {
                "has_suspicious": False,
                "patterns": [],
                "confidence_penalty": 0.0
            }
            
            # 检查可疑字符
            suspicious_chars_found = []
            for char in text:
                if char in self.suspicious_chars:
                    suspicious_chars_found.append(char)
            
            if suspicious_chars_found:
                suspicious["has_suspicious"] = True
                suspicious["patterns"].append(f"可疑字符: {set(suspicious_chars_found)}")
                suspicious["confidence_penalty"] += 0.2
            
            # 检查错误模式
            for i, pattern in enumerate(self.error_patterns):
                if pattern.search(text):
                    suspicious["has_suspicious"] = True
                    suspicious["patterns"].append(f"错误模式{i+1}")
                    suspicious["confidence_penalty"] += 0.1
            
            # 检查重复字符
            repeated_chars = self._find_repeated_chars(text)
            if repeated_chars:
                suspicious["has_suspicious"] = True
                suspicious["patterns"].append(f"重复字符: {repeated_chars}")
                suspicious["confidence_penalty"] += 0.1
            
            # 检查乱码模式
            if self._is_garbled_text(text):
                suspicious["has_suspicious"] = True
                suspicious["patterns"].append("疑似乱码")
                suspicious["confidence_penalty"] += 0.3
            
            return suspicious
            
        except Exception as e:
            logger.error(f"可疑模式检测失败: {e}")
            return {
                "has_suspicious": True,
                "patterns": [f"检测错误: {str(e)}"],
                "confidence_penalty": 0.5
            }
    
    def _find_repeated_chars(self, text: str, min_length: int = 4) -> List[str]:
        """查找重复字符模式"""
        repeated = []
        
        try:
            i = 0
            while i < len(text):
                char = text[i]
                count = 1
                
                # 计算连续相同字符的数量
                while i + count < len(text) and text[i + count] == char:
                    count += 1
                
                # 如果重复次数超过阈值，记录
                if count >= min_length:
                    repeated.append(f"{char}×{count}")
                
                i += count
            
            return repeated
            
        except Exception as e:
            logger.error(f"查找重复字符失败: {e}")
            return []
    
    def _is_garbled_text(self, text: str) -> bool:
        """检测是否为乱码"""
        try:
            # 检查Unicode类别分布
            categories = {}
            for char in text:
                category = unicodedata.category(char)
                categories[category] = categories.get(category, 0) + 1
            
            # 如果包含过多控制字符或未定义字符，可能是乱码
            control_chars = categories.get('Cc', 0) + categories.get('Cn', 0)
            total_chars = len(text)
            
            if total_chars > 0 and control_chars / total_chars > 0.1:
                return True
            
            # 检查是否包含过多罕见Unicode字符
            rare_chars = 0
            for char in text:
                if ord(char) > 0xFFFF:  # 超出基本多文种平面
                    rare_chars += 1
            
            if total_chars > 0 and rare_chars / total_chars > 0.2:
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"乱码检测失败: {e}")
            return False
    
    def _validate_financial_term(self, text: str) -> bool:
        """验证金融术语"""
        try:
            financial_keywords = {
                "金额", "总价", "单价", "数量", "小计", "合计", "税率", "税额",
                "含税", "不含税", "人民币", "元", "万元", "千元", "美元", "欧元",
                "价格", "费用", "成本", "利润", "收入", "支出", "余额", "结算"
            }
            
            # 检查是否包含金融关键词
            for keyword in financial_keywords:
                if keyword in text:
                    return True
            
            # 检查是否为货币格式
            currency_pattern = re.compile(r'[¥$€£]\s*\d+')
            if currency_pattern.search(text):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"金融术语验证失败: {e}")
            return False
    
    def _calculate_overall_validation(self, type_validation: Dict, general_validation: Dict, 
                                    suspicious_patterns: Dict, char_stats: Dict) -> Dict:
        """计算综合验证结果"""
        try:
            # 基础置信度
            base_confidence = min(
                type_validation.get("confidence", 1.0),
                general_validation.get("confidence", 1.0)
            )
            
            # 应用可疑模式惩罚
            confidence_penalty = suspicious_patterns.get("confidence_penalty", 0.0)
            final_confidence = max(0.0, base_confidence - confidence_penalty)
            
            # 综合有效性判断
            is_valid = (
                type_validation.get("is_valid", True) and
                general_validation.get("is_valid", True) and
                not suspicious_patterns.get("has_suspicious", False)
            )
            
            # 如果置信度过低，标记为无效
            if final_confidence < 0.3:
                is_valid = False
            
            # 收集所有问题
            all_issues = []
            all_issues.extend(type_validation.get("issues", []))
            all_issues.extend(general_validation.get("issues", []))
            all_issues.extend(suspicious_patterns.get("patterns", []))
            
            return {
                "is_valid": is_valid,
                "confidence": final_confidence,
                "issues": all_issues
            }
            
        except Exception as e:
            logger.error(f"计算综合验证结果失败: {e}")
            return {
                "is_valid": False,
                "confidence": 0.0,
                "issues": [f"综合验证错误: {str(e)}"]
            }
    
    def batch_validate(self, texts: List[str], text_types: Optional[List[str]] = None) -> List[Dict]:
        """批量验证文本"""
        try:
            if text_types is None:
                text_types = ["text"] * len(texts)
            
            results = []
            for i, text in enumerate(texts):
                text_type = text_types[i] if i < len(text_types) else "text"
                result = self.validate_text(text, text_type)
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"批量验证失败: {e}")
            return [{"is_valid": False, "error": str(e)} for _ in texts]
