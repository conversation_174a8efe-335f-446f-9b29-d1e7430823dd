"""
图像处理工具函数
"""
import os
import hashlib
from typing import <PERSON>ple, Optional, List
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageFilter
from loguru import logger


def load_image(image_path: str) -> np.ndarray:
    """
    加载图像文件
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        图像数组
    """
    try:
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图像文件不存在: {image_path}")
        
        # 使用OpenCV加载图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像文件: {image_path}")
        
        # 转换为RGB格式
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image
        
    except Exception as e:
        logger.error(f"加载图像失败: {e}")
        raise


def save_image(image: np.ndarray, output_path: str, quality: int = 95) -> bool:
    """
    保存图像文件
    
    Args:
        image: 图像数组
        output_path: 输出路径
        quality: 图像质量(1-100)
        
    Returns:
        是否保存成功
    """
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 转换为PIL图像
        if len(image.shape) == 3:
            pil_image = Image.fromarray(image)
        else:
            pil_image = Image.fromarray(image, mode='L')
        
        # 保存图像
        if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
            pil_image.save(output_path, 'JPEG', quality=quality, optimize=True)
        else:
            pil_image.save(output_path, optimize=True)
        
        return True
        
    except Exception as e:
        logger.error(f"保存图像失败: {e}")
        return False


def get_image_info(image_path: str) -> dict:
    """
    获取图像基本信息
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        图像信息字典
    """
    try:
        with Image.open(image_path) as img:
            info = {
                "width": img.width,
                "height": img.height,
                "mode": img.mode,
                "format": img.format,
                "file_size": os.path.getsize(image_path),
                "dpi": img.info.get('dpi', (72, 72)),
                "has_transparency": img.mode in ('RGBA', 'LA') or 'transparency' in img.info
            }
            
            # 计算文件哈希
            info["md5"] = calculate_image_hash(image_path)
            
            return info
            
    except Exception as e:
        logger.error(f"获取图像信息失败: {e}")
        return {"error": str(e)}


def calculate_image_hash(image_path: str) -> str:
    """
    计算图像文件的MD5哈希值
    
    Args:
        image_path: 图像文件路径
        
    Returns:
        MD5哈希值
    """
    try:
        hash_md5 = hashlib.md5()
        with open(image_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
        
    except Exception as e:
        logger.error(f"计算图像哈希失败: {e}")
        return ""


def resize_image(image: np.ndarray, target_size: Tuple[int, int], keep_aspect_ratio: bool = True) -> np.ndarray:
    """
    调整图像尺寸
    
    Args:
        image: 输入图像
        target_size: 目标尺寸 (width, height)
        keep_aspect_ratio: 是否保持宽高比
        
    Returns:
        调整后的图像
    """
    try:
        height, width = image.shape[:2]
        target_width, target_height = target_size
        
        if keep_aspect_ratio:
            # 计算缩放比例
            scale = min(target_width / width, target_height / height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            
            # 调整尺寸
            resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
            
            # 创建目标尺寸的画布并居中放置
            canvas = np.ones((target_height, target_width, image.shape[2]), dtype=image.dtype) * 255
            y_offset = (target_height - new_height) // 2
            x_offset = (target_width - new_width) // 2
            canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
            
            return canvas
        else:
            return cv2.resize(image, target_size, interpolation=cv2.INTER_LANCZOS4)
            
    except Exception as e:
        logger.error(f"调整图像尺寸失败: {e}")
        return image


def crop_image(image: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
    """
    裁剪图像
    
    Args:
        image: 输入图像
        bbox: 边界框 (x1, y1, x2, y2)
        
    Returns:
        裁剪后的图像
    """
    try:
        x1, y1, x2, y2 = bbox
        height, width = image.shape[:2]
        
        # 确保坐标在有效范围内
        x1 = max(0, min(x1, width))
        y1 = max(0, min(y1, height))
        x2 = max(x1, min(x2, width))
        y2 = max(y1, min(y2, height))
        
        return image[y1:y2, x1:x2]
        
    except Exception as e:
        logger.error(f"裁剪图像失败: {e}")
        return image


def convert_to_grayscale(image: np.ndarray) -> np.ndarray:
    """
    转换为灰度图像
    
    Args:
        image: 输入图像
        
    Returns:
        灰度图像
    """
    try:
        if len(image.shape) == 3:
            return cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        return image
        
    except Exception as e:
        logger.error(f"转换灰度图像失败: {e}")
        return image


def enhance_contrast(image: np.ndarray, factor: float = 1.5) -> np.ndarray:
    """
    增强图像对比度
    
    Args:
        image: 输入图像
        factor: 增强因子
        
    Returns:
        增强后的图像
    """
    try:
        # 转换为PIL图像
        pil_image = Image.fromarray(image)
        
        # 增强对比度
        enhancer = ImageEnhance.Contrast(pil_image)
        enhanced = enhancer.enhance(factor)
        
        return np.array(enhanced)
        
    except Exception as e:
        logger.error(f"增强对比度失败: {e}")
        return image


def apply_gaussian_blur(image: np.ndarray, kernel_size: int = 5, sigma: float = 1.0) -> np.ndarray:
    """
    应用高斯模糊
    
    Args:
        image: 输入图像
        kernel_size: 核大小
        sigma: 标准差
        
    Returns:
        模糊后的图像
    """
    try:
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
        
    except Exception as e:
        logger.error(f"应用高斯模糊失败: {e}")
        return image


def batch_process_images(image_paths: List[str], process_func, output_dir: str, **kwargs) -> dict:
    """
    批量处理图像
    
    Args:
        image_paths: 图像路径列表
        process_func: 处理函数
        output_dir: 输出目录
        **kwargs: 处理函数参数
        
    Returns:
        处理结果
    """
    results = {
        "success": [],
        "failed": [],
        "total": len(image_paths)
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    for image_path in image_paths:
        try:
            # 加载图像
            image = load_image(image_path)
            
            # 处理图像
            processed_image = process_func(image, **kwargs)
            
            # 保存处理后的图像
            filename = os.path.basename(image_path)
            output_path = os.path.join(output_dir, f"processed_{filename}")
            
            if save_image(processed_image, output_path):
                results["success"].append({
                    "input_path": image_path,
                    "output_path": output_path
                })
            else:
                results["failed"].append({
                    "input_path": image_path,
                    "error": "保存失败"
                })
                
        except Exception as e:
            logger.error(f"批量处理图像失败 {image_path}: {e}")
            results["failed"].append({
                "input_path": image_path,
                "error": str(e)
            })
    
    return results
