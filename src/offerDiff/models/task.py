"""
任务数据模型
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean, Float, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 等待中
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class DifferenceType(str, Enum):
    """差异类型枚举"""
    AMOUNT = "amount"  # 金额差异
    QUANTITY = "quantity"  # 数量差异
    TEXT = "text"  # 文本差异
    TABLE = "table"  # 表格差异
    LAYOUT = "layout"  # 布局差异
    SIGNATURE = "signature"  # 签章差异


class SeverityLevel(str, Enum):
    """严重程度枚举"""
    HIGH = "high"  # 高
    MEDIUM = "medium"  # 中
    LOW = "low"  # 低


class TaskModel(Base):
    """比对任务数据模型"""
    __tablename__ = "tasks"
    
    # 主键
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 文件关联
    bid_file_id = Column(String, ForeignKey("files.id"), nullable=False, comment="投标文件ID")
    contract_file_id = Column(String, ForeignKey("files.id"), nullable=False, comment="合同文件ID")
    
    # 任务信息
    status = Column(String(20), default=TaskStatus.PENDING, comment="任务状态")
    progress = Column(Integer, default=0, comment="处理进度(0-100)")
    
    # 比对配置
    strict_mode = Column(Boolean, default=True, comment="严格模式")
    ignore_scan_quality = Column(Boolean, default=False, comment="忽略扫描质量差异")
    focus_areas = Column(Text, comment="重点关注区域(JSON)")
    
    # 比对结果
    overall_match = Column(Boolean, comment="整体是否匹配")
    confidence_score = Column(Float, comment="置信度评分(0-1)")
    differences_count = Column(Integer, default=0, comment="差异总数")
    high_severity_count = Column(Integer, default=0, comment="高严重程度差异数")
    medium_severity_count = Column(Integer, default=0, comment="中严重程度差异数")
    low_severity_count = Column(Integer, default=0, comment="低严重程度差异数")
    
    # 处理信息
    processing_time = Column(Integer, comment="处理时间(秒)")
    error_message = Column(Text, comment="错误信息")
    processing_log = Column(Text, comment="处理日志")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    started_at = Column(DateTime, comment="开始处理时间")
    completed_at = Column(DateTime, comment="完成时间")
    
    # 可选字段
    user_id = Column(String(50), comment="用户ID")
    project_id = Column(String(50), comment="项目ID")
    description = Column(Text, comment="任务描述")
    
    def __repr__(self):
        return f"<TaskModel(id={self.id}, status={self.status}, progress={self.progress})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "bid_file_id": self.bid_file_id,
            "contract_file_id": self.contract_file_id,
            "status": self.status,
            "progress": self.progress,
            "strict_mode": self.strict_mode,
            "ignore_scan_quality": self.ignore_scan_quality,
            "overall_match": self.overall_match,
            "confidence_score": self.confidence_score,
            "differences_count": self.differences_count,
            "high_severity_count": self.high_severity_count,
            "medium_severity_count": self.medium_severity_count,
            "low_severity_count": self.low_severity_count,
            "processing_time": self.processing_time,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "user_id": self.user_id,
            "project_id": self.project_id,
            "description": self.description
        }


class ComparisonResult(Base):
    """比对结果详情数据模型"""
    __tablename__ = "comparison_results"
    
    # 主键
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 任务关联
    task_id = Column(String, ForeignKey("tasks.id"), nullable=False, comment="任务ID")
    
    # 差异信息
    difference_type = Column(String(20), nullable=False, comment="差异类型")
    severity = Column(String(10), nullable=False, comment="严重程度")
    location = Column(String(200), comment="差异位置")
    page_number = Column(Integer, comment="页码")
    
    # 差异内容
    bid_value = Column(Text, comment="投标文件中的值")
    contract_value = Column(Text, comment="合同文件中的值")
    description = Column(Text, comment="差异描述")
    
    # 坐标信息
    bbox_x1 = Column(Integer, comment="边界框左上角X坐标")
    bbox_y1 = Column(Integer, comment="边界框左上角Y坐标")
    bbox_x2 = Column(Integer, comment="边界框右下角X坐标")
    bbox_y2 = Column(Integer, comment="边界框右下角Y坐标")
    
    # 置信度
    confidence = Column(Float, comment="差异检测置信度")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    def __repr__(self):
        return f"<ComparisonResult(id={self.id}, type={self.difference_type}, severity={self.severity})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "difference_type": self.difference_type,
            "severity": self.severity,
            "location": self.location,
            "page_number": self.page_number,
            "bid_value": self.bid_value,
            "contract_value": self.contract_value,
            "description": self.description,
            "bbox": {
                "x1": self.bbox_x1,
                "y1": self.bbox_y1,
                "x2": self.bbox_x2,
                "y2": self.bbox_y2
            } if all([self.bbox_x1, self.bbox_y1, self.bbox_x2, self.bbox_y2]) else None,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
