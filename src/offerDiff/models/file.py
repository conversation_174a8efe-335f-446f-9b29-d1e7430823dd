"""
文件数据模型
"""
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Column, String, Integer, DateTime, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class FileType(str, Enum):
    """文件类型枚举"""
    BID = "bid"  # 投标文件
    CONTRACT = "contract"  # 合同文件


class FileStatus(str, Enum):
    """文件状态枚举"""
    UPLOADED = "uploaded"  # 已上传
    PROCESSING = "processing"  # 处理中
    PROCESSED = "processed"  # 已处理
    ERROR = "error"  # 处理错误


class FileModel(Base):
    """文件数据模型"""
    __tablename__ = "files"
    
    # 主键
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 基本信息
    original_name = Column(String(255), nullable=False, comment="原始文件名")
    file_name = Column(String(255), nullable=False, comment="存储文件名")
    file_path = Column(String(500), nullable=False, comment="文件存储路径")
    file_size = Column(Integer, nullable=False, comment="文件大小(字节)")
    file_type = Column(String(20), nullable=False, comment="文件类型")
    
    # 状态信息
    status = Column(String(20), default=FileStatus.UPLOADED, comment="处理状态")
    
    # PDF信息
    page_count = Column(Integer, comment="PDF页数")
    
    # 处理结果
    ocr_result = Column(Text, comment="OCR识别结果(JSON)")
    llm_result = Column(Text, comment="大模型分析结果(JSON)")
    processing_log = Column(Text, comment="处理日志")
    
    # 质量评估
    scan_quality_score = Column(Integer, comment="扫描质量评分(0-100)")
    ocr_confidence = Column(Integer, comment="OCR识别置信度(0-100)")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    processed_at = Column(DateTime, comment="处理完成时间")
    
    # 可选字段
    project_id = Column(String(50), comment="项目ID")
    user_id = Column(String(50), comment="用户ID")
    description = Column(Text, comment="文件描述")
    
    def __repr__(self):
        return f"<FileModel(id={self.id}, name={self.original_name}, status={self.status})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "original_name": self.original_name,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "status": self.status,
            "page_count": self.page_count,
            "scan_quality_score": self.scan_quality_score,
            "ocr_confidence": self.ocr_confidence,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "project_id": self.project_id,
            "user_id": self.user_id,
            "description": self.description
        }
