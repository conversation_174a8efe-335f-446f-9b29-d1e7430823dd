"""
OCR结果数据模型
"""
import json
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum


class TextType(str, Enum):
    """文本类型枚举"""
    TEXT = "text"
    NUMBER = "number"
    CURRENCY = "currency"
    PERCENTAGE = "percentage"
    DATE = "date"
    TIME = "time"
    PHONE = "phone"
    EMAIL = "email"
    FINANCIAL_TERM = "financial_term"


class ProcessingStatus(str, Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class BoundingBox:
    """边界框数据类"""
    x1: int
    y1: int
    x2: int
    y2: int
    
    @property
    def width(self) -> int:
        return self.x2 - self.x1
    
    @property
    def height(self) -> int:
        return self.y2 - self.y1
    
    @property
    def area(self) -> int:
        return self.width * self.height
    
    @property
    def center(self) -> tuple:
        return ((self.x1 + self.x2) // 2, (self.y1 + self.y2) // 2)
    
    def to_dict(self) -> Dict:
        return asdict(self)


@dataclass
class TextBlock:
    """文本块数据类"""
    id: str
    text: str
    original_text: str
    confidence: float
    text_type: TextType
    bbox: BoundingBox
    polygon: List[List[int]]
    
    # 验证信息
    is_valid: bool = True
    validation_issues: List[str] = None
    
    # 处理信息
    processing_steps: Dict[str, str] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.validation_issues is None:
            self.validation_issues = []
        if self.processing_steps is None:
            self.processing_steps = {}
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['bbox'] = self.bbox.to_dict()
        result['created_at'] = self.created_at.isoformat() if self.created_at else None
        return result


@dataclass
class TableCell:
    """表格单元格数据类"""
    id: str
    row: int
    col: int
    text: str
    original_text: str
    confidence: float
    text_type: TextType
    bbox: BoundingBox
    
    # 合并信息
    is_merged: bool = False
    merged_cells: List[str] = None
    rowspan: int = 1
    colspan: int = 1
    
    # 验证信息
    is_valid: bool = True
    validation_issues: List[str] = None
    
    def __post_init__(self):
        if self.merged_cells is None:
            self.merged_cells = []
        if self.validation_issues is None:
            self.validation_issues = []
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['bbox'] = self.bbox.to_dict()
        return result


@dataclass
class Table:
    """表格数据类"""
    id: str
    bbox: BoundingBox
    cells: List[TableCell]
    rows: int
    columns: int
    
    # 结构信息
    has_header: bool = False
    header_row: Optional[int] = None
    table_type: str = "standard"
    
    # HTML表示
    html: str = ""
    
    # 质量信息
    confidence: float = 0.0
    structure_confidence: float = 0.0
    
    # 一致性检查
    consistency_issues: List[str] = None
    
    def __post_init__(self):
        if self.consistency_issues is None:
            self.consistency_issues = []
        if not self.id:
            self.id = str(uuid.uuid4())
    
    def get_cell(self, row: int, col: int) -> Optional[TableCell]:
        """获取指定位置的单元格"""
        for cell in self.cells:
            if cell.row == row and cell.col == col:
                return cell
        return None
    
    def get_row_cells(self, row: int) -> List[TableCell]:
        """获取指定行的所有单元格"""
        return [cell for cell in self.cells if cell.row == row]
    
    def get_column_cells(self, col: int) -> List[TableCell]:
        """获取指定列的所有单元格"""
        return [cell for cell in self.cells if cell.col == col]
    
    def to_dict(self) -> Dict:
        result = asdict(self)
        result['bbox'] = self.bbox.to_dict()
        result['cells'] = [cell.to_dict() for cell in self.cells]
        return result


@dataclass
class OCRResult:
    """OCR结果数据类"""
    id: str
    file_id: str
    image_path: str
    
    # 识别结果
    text_blocks: List[TextBlock]
    tables: List[Table]
    full_text: str
    
    # 质量信息
    overall_confidence: float
    text_confidence: float
    table_confidence: float
    
    # 处理信息
    status: ProcessingStatus
    processing_time: float = 0.0
    error_message: Optional[str] = None
    
    # 统计信息
    total_text_blocks: int = 0
    total_tables: int = 0
    total_cells: int = 0
    
    # 时间戳
    created_at: datetime = None
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
        if not self.id:
            self.id = str(uuid.uuid4())
        
        # 更新统计信息
        self.total_text_blocks = len(self.text_blocks)
        self.total_tables = len(self.tables)
        self.total_cells = sum(len(table.cells) for table in self.tables)
    
    def add_text_block(self, text_block: TextBlock):
        """添加文本块"""
        self.text_blocks.append(text_block)
        self.total_text_blocks = len(self.text_blocks)
        self.updated_at = datetime.utcnow()
    
    def add_table(self, table: Table):
        """添加表格"""
        self.tables.append(table)
        self.total_tables = len(self.tables)
        self.total_cells = sum(len(table.cells) for table in self.tables)
        self.updated_at = datetime.utcnow()
    
    def get_text_by_type(self, text_type: TextType) -> List[TextBlock]:
        """根据类型获取文本块"""
        return [block for block in self.text_blocks if block.text_type == text_type]
    
    def get_financial_data(self) -> Dict:
        """提取财务数据"""
        financial_data = {
            "currencies": [],
            "numbers": [],
            "percentages": [],
            "financial_terms": []
        }
        
        # 从文本块提取
        for block in self.text_blocks:
            if block.text_type == TextType.CURRENCY:
                financial_data["currencies"].append(block.text)
            elif block.text_type == TextType.NUMBER:
                financial_data["numbers"].append(block.text)
            elif block.text_type == TextType.PERCENTAGE:
                financial_data["percentages"].append(block.text)
            elif block.text_type == TextType.FINANCIAL_TERM:
                financial_data["financial_terms"].append(block.text)
        
        # 从表格提取
        for table in self.tables:
            for cell in table.cells:
                if cell.text_type == TextType.CURRENCY:
                    financial_data["currencies"].append(cell.text)
                elif cell.text_type == TextType.NUMBER:
                    financial_data["numbers"].append(cell.text)
                elif cell.text_type == TextType.PERCENTAGE:
                    financial_data["percentages"].append(cell.text)
                elif cell.text_type == TextType.FINANCIAL_TERM:
                    financial_data["financial_terms"].append(cell.text)
        
        return financial_data
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        result = asdict(self)
        result['text_blocks'] = [block.to_dict() for block in self.text_blocks]
        result['tables'] = [table.to_dict() for table in self.tables]
        result['created_at'] = self.created_at.isoformat() if self.created_at else None
        result['updated_at'] = self.updated_at.isoformat() if self.updated_at else None
        return result
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'OCRResult':
        """从字典创建实例"""
        # 转换文本块
        text_blocks = []
        for block_data in data.get('text_blocks', []):
            bbox = BoundingBox(**block_data['bbox'])
            block_data['bbox'] = bbox
            if 'created_at' in block_data and block_data['created_at']:
                block_data['created_at'] = datetime.fromisoformat(block_data['created_at'])
            text_blocks.append(TextBlock(**block_data))
        
        # 转换表格
        tables = []
        for table_data in data.get('tables', []):
            # 转换单元格
            cells = []
            for cell_data in table_data['cells']:
                bbox = BoundingBox(**cell_data['bbox'])
                cell_data['bbox'] = bbox
                cells.append(TableCell(**cell_data))
            
            # 转换表格边界框
            table_bbox = BoundingBox(**table_data['bbox'])
            table_data['bbox'] = table_bbox
            table_data['cells'] = cells
            tables.append(Table(**table_data))
        
        # 转换时间戳
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'updated_at' in data and data['updated_at']:
            data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        
        data['text_blocks'] = text_blocks
        data['tables'] = tables
        
        return cls(**data)


class OCRResultBuilder:
    """OCR结果构建器"""
    
    def __init__(self, file_id: str, image_path: str):
        self.file_id = file_id
        self.image_path = image_path
        self.text_blocks = []
        self.tables = []
        self.processing_start_time = datetime.utcnow()
    
    def add_text_block(self, text: str, original_text: str, confidence: float, 
                      text_type: TextType, bbox: BoundingBox, polygon: List[List[int]],
                      validation_info: Optional[Dict] = None) -> str:
        """添加文本块"""
        block_id = str(uuid.uuid4())
        
        block = TextBlock(
            id=block_id,
            text=text,
            original_text=original_text,
            confidence=confidence,
            text_type=text_type,
            bbox=bbox,
            polygon=polygon,
            is_valid=validation_info.get('is_valid', True) if validation_info else True,
            validation_issues=validation_info.get('issues', []) if validation_info else []
        )
        
        self.text_blocks.append(block)
        return block_id
    
    def add_table(self, bbox: BoundingBox, cells: List[TableCell], 
                 rows: int, columns: int, **kwargs) -> str:
        """添加表格"""
        table_id = str(uuid.uuid4())
        
        table = Table(
            id=table_id,
            bbox=bbox,
            cells=cells,
            rows=rows,
            columns=columns,
            **kwargs
        )
        
        self.tables.append(table)
        return table_id
    
    def build(self, status: ProcessingStatus = ProcessingStatus.COMPLETED, 
             error_message: Optional[str] = None) -> OCRResult:
        """构建OCR结果"""
        processing_time = (datetime.utcnow() - self.processing_start_time).total_seconds()
        
        # 计算置信度
        text_confidences = [block.confidence for block in self.text_blocks]
        table_confidences = [table.confidence for table in self.tables]
        
        text_confidence = sum(text_confidences) / len(text_confidences) if text_confidences else 0.0
        table_confidence = sum(table_confidences) / len(table_confidences) if table_confidences else 0.0
        overall_confidence = (text_confidence + table_confidence) / 2 if (text_confidences or table_confidences) else 0.0
        
        # 生成完整文本
        full_text_parts = []
        for block in self.text_blocks:
            full_text_parts.append(block.text)
        
        for table in self.tables:
            table_text = []
            for cell in table.cells:
                if cell.text.strip():
                    table_text.append(cell.text)
            if table_text:
                full_text_parts.append(" | ".join(table_text))
        
        full_text = "\n".join(full_text_parts)
        
        return OCRResult(
            id=str(uuid.uuid4()),
            file_id=self.file_id,
            image_path=self.image_path,
            text_blocks=self.text_blocks,
            tables=self.tables,
            full_text=full_text,
            overall_confidence=overall_confidence,
            text_confidence=text_confidence,
            table_confidence=table_confidence,
            status=status,
            processing_time=processing_time,
            error_message=error_message
        )
