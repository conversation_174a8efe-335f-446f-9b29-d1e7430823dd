"""
差异分析器
"""
import re
from typing import Dict, List, Optional, Any, Tuple
from loguru import logger

from src.offerDiff.models.ocr_result import OCRResult, TextType


class DifferenceAnalyzer:
    """差异分析器"""
    
    def __init__(self):
        self.critical_keywords = {
            "金额", "总价", "单价", "合计", "小计", "税额", "税率",
            "数量", "单位", "规格", "型号", "品名", "货物",
            "甲方", "乙方", "买方", "卖方", "供方", "需方",
            "日期", "期限", "交货", "付款", "结算", "违约"
        }
        
        self.major_keywords = {
            "地址", "联系人", "电话", "传真", "邮箱",
            "备注", "说明", "条款", "条件", "要求"
        }
        
        self.amount_tolerance = 0.01  # 金额容差
        self.percentage_tolerance = 0.001  # 百分比容差
    
    async def analyze_differences(self, ocr_result1: OCRResult, ocr_result2: OCRResult, 
                                comparison_results: Dict) -> Dict:
        """
        分析两个文档的差异
        
        Args:
            ocr_result1: 第一个OCR结果
            ocr_result2: 第二个OCR结果
            comparison_results: 各项比对结果
            
        Returns:
            差异分析结果
        """
        try:
            # 分析文本差异
            text_differences = self._analyze_text_differences(
                ocr_result1, ocr_result2, comparison_results.get("text_comparison", {})
            )
            
            # 分析布局差异
            layout_differences = self._analyze_layout_differences(
                ocr_result1, ocr_result2, comparison_results.get("layout_comparison", {})
            )
            
            # 分析财务数据差异
            financial_differences = self._analyze_financial_differences(
                ocr_result1, ocr_result2, comparison_results.get("financial_comparison", {})
            )
            
            # 分析语义差异
            semantic_differences = self._analyze_semantic_differences(
                comparison_results.get("semantic_comparison", {})
            )
            
            # 合并和分类差异
            all_differences = (
                text_differences + layout_differences + 
                financial_differences + semantic_differences
            )
            
            # 按严重程度分类
            categorized_differences = self._categorize_differences(all_differences)
            
            # 生成差异摘要
            difference_summary = self._generate_difference_summary(categorized_differences)
            
            result = {
                "critical_differences": categorized_differences["critical"],
                "major_differences": categorized_differences["major"],
                "minor_differences": categorized_differences["minor"],
                "total_differences": len(all_differences),
                "difference_summary": difference_summary,
                "analysis_details": {
                    "text_differences_count": len(text_differences),
                    "layout_differences_count": len(layout_differences),
                    "financial_differences_count": len(financial_differences),
                    "semantic_differences_count": len(semantic_differences)
                }
            }
            
            logger.info(f"差异分析完成: {len(all_differences)}个差异")
            return result
            
        except Exception as e:
            logger.error(f"差异分析失败: {e}")
            return {
                "critical_differences": [],
                "major_differences": [],
                "minor_differences": [],
                "total_differences": 0,
                "error": str(e)
            }
    
    def _analyze_text_differences(self, ocr_result1: OCRResult, ocr_result2: OCRResult, 
                                text_comparison: Dict) -> List[Dict]:
        """分析文本差异"""
        differences = []
        
        try:
            # 分析文本块差异
            text_blocks1 = {i: block for i, block in enumerate(ocr_result1.text_blocks)}
            text_blocks2 = {i: block for i, block in enumerate(ocr_result2.text_blocks)}
            
            # 查找缺失的文本块
            for i, block1 in text_blocks1.items():
                if not self._find_matching_text_block(block1, text_blocks2.values()):
                    differences.append({
                        "type": "text_missing",
                        "category": "content",
                        "description": f"文档2中缺失文本: {block1.text[:50]}...",
                        "document": "document2",
                        "content": block1.text,
                        "position": {
                            "x": block1.bbox.x1,
                            "y": block1.bbox.y1
                        },
                        "severity": self._assess_text_severity(block1.text)
                    })
            
            for i, block2 in text_blocks2.items():
                if not self._find_matching_text_block(block2, text_blocks1.values()):
                    differences.append({
                        "type": "text_added",
                        "category": "content",
                        "description": f"文档2中新增文本: {block2.text[:50]}...",
                        "document": "document2",
                        "content": block2.text,
                        "position": {
                            "x": block2.bbox.x1,
                            "y": block2.bbox.y1
                        },
                        "severity": self._assess_text_severity(block2.text)
                    })
            
            # 分析文本内容变化
            content_differences = self._analyze_content_changes(ocr_result1, ocr_result2)
            differences.extend(content_differences)
            
            return differences
            
        except Exception as e:
            logger.error(f"分析文本差异失败: {e}")
            return []
    
    def _find_matching_text_block(self, target_block, candidate_blocks) -> bool:
        """查找匹配的文本块"""
        try:
            for candidate in candidate_blocks:
                # 简单的文本相似度匹配
                if target_block.text == candidate.text:
                    return True
                
                # 模糊匹配
                if len(target_block.text) > 10 and len(candidate.text) > 10:
                    # 计算简单的相似度
                    common_chars = set(target_block.text) & set(candidate.text)
                    similarity = len(common_chars) / max(len(set(target_block.text)), len(set(candidate.text)), 1)
                    if similarity > 0.8:
                        return True
            
            return False
            
        except Exception as e:
            logger.error(f"查找匹配文本块失败: {e}")
            return False
    
    def _assess_text_severity(self, text: str) -> str:
        """评估文本差异的严重程度"""
        try:
            text_lower = text.lower()
            
            # 检查是否包含关键词
            for keyword in self.critical_keywords:
                if keyword in text:
                    return "critical"
            
            for keyword in self.major_keywords:
                if keyword in text:
                    return "major"
            
            # 检查是否包含数字或金额
            if re.search(r'\d+', text) or any(symbol in text for symbol in ["¥", "$", "€", "£"]):
                return "major"
            
            return "minor"
            
        except Exception as e:
            logger.error(f"评估文本严重程度失败: {e}")
            return "minor"
    
    def _analyze_content_changes(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> List[Dict]:
        """分析内容变化"""
        differences = []
        
        try:
            # 提取关键信息进行比对
            key_info1 = self._extract_key_information(ocr_result1)
            key_info2 = self._extract_key_information(ocr_result2)
            
            # 比较金额
            amounts1 = key_info1.get("amounts", [])
            amounts2 = key_info2.get("amounts", [])
            
            amount_diffs = self._compare_amounts(amounts1, amounts2)
            differences.extend(amount_diffs)
            
            # 比较日期
            dates1 = key_info1.get("dates", [])
            dates2 = key_info2.get("dates", [])
            
            date_diffs = self._compare_dates(dates1, dates2)
            differences.extend(date_diffs)
            
            # 比较关键词
            keywords1 = key_info1.get("keywords", [])
            keywords2 = key_info2.get("keywords", [])
            
            keyword_diffs = self._compare_keywords(keywords1, keywords2)
            differences.extend(keyword_diffs)
            
            return differences
            
        except Exception as e:
            logger.error(f"分析内容变化失败: {e}")
            return []
    
    def _extract_key_information(self, ocr_result: OCRResult) -> Dict:
        """提取关键信息"""
        try:
            key_info = {
                "amounts": [],
                "dates": [],
                "keywords": []
            }
            
            # 从文本块提取
            for block in ocr_result.text_blocks:
                text = block.text
                
                # 提取金额
                amounts = re.findall(r'[¥$€£]\s*[\d,]+\.?\d*', text)
                key_info["amounts"].extend(amounts)
                
                # 提取日期
                dates = re.findall(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', text)
                key_info["dates"].extend(dates)
                
                # 提取关键词
                for keyword in self.critical_keywords | self.major_keywords:
                    if keyword in text:
                        key_info["keywords"].append(keyword)
            
            # 从表格提取
            for table in ocr_result.tables:
                for cell in table.cells:
                    text = cell.text
                    
                    # 提取金额
                    amounts = re.findall(r'[¥$€£]\s*[\d,]+\.?\d*', text)
                    key_info["amounts"].extend(amounts)
                    
                    # 提取日期
                    dates = re.findall(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', text)
                    key_info["dates"].extend(dates)
            
            return key_info
            
        except Exception as e:
            logger.error(f"提取关键信息失败: {e}")
            return {"amounts": [], "dates": [], "keywords": []}
    
    def _compare_amounts(self, amounts1: List[str], amounts2: List[str]) -> List[Dict]:
        """比较金额"""
        differences = []
        
        try:
            # 转换为数值进行比较
            values1 = []
            values2 = []
            
            for amount in amounts1:
                try:
                    value = float(re.sub(r'[^\d.]', '', amount))
                    values1.append((amount, value))
                except ValueError:
                    continue
            
            for amount in amounts2:
                try:
                    value = float(re.sub(r'[^\d.]', '', amount))
                    values2.append((amount, value))
                except ValueError:
                    continue
            
            # 查找差异
            for amount1, value1 in values1:
                found_match = False
                for amount2, value2 in values2:
                    if abs(value1 - value2) <= self.amount_tolerance:
                        found_match = True
                        break
                
                if not found_match:
                    differences.append({
                        "type": "amount_difference",
                        "category": "financial",
                        "description": f"金额差异: 文档1中的{amount1}在文档2中未找到匹配",
                        "document": "document1",
                        "content": amount1,
                        "severity": "critical"
                    })
            
            for amount2, value2 in values2:
                found_match = False
                for amount1, value1 in values1:
                    if abs(value1 - value2) <= self.amount_tolerance:
                        found_match = True
                        break
                
                if not found_match:
                    differences.append({
                        "type": "amount_difference",
                        "category": "financial",
                        "description": f"金额差异: 文档2中的{amount2}在文档1中未找到匹配",
                        "document": "document2",
                        "content": amount2,
                        "severity": "critical"
                    })
            
            return differences
            
        except Exception as e:
            logger.error(f"比较金额失败: {e}")
            return []
    
    def _compare_dates(self, dates1: List[str], dates2: List[str]) -> List[Dict]:
        """比较日期"""
        differences = []
        
        try:
            set1 = set(dates1)
            set2 = set(dates2)
            
            # 查找缺失的日期
            missing_in_doc2 = set1 - set2
            missing_in_doc1 = set2 - set1
            
            for date in missing_in_doc2:
                differences.append({
                    "type": "date_missing",
                    "category": "temporal",
                    "description": f"日期差异: {date}在文档2中缺失",
                    "document": "document2",
                    "content": date,
                    "severity": "major"
                })
            
            for date in missing_in_doc1:
                differences.append({
                    "type": "date_added",
                    "category": "temporal",
                    "description": f"日期差异: {date}在文档1中缺失",
                    "document": "document1",
                    "content": date,
                    "severity": "major"
                })
            
            return differences
            
        except Exception as e:
            logger.error(f"比较日期失败: {e}")
            return []
    
    def _compare_keywords(self, keywords1: List[str], keywords2: List[str]) -> List[Dict]:
        """比较关键词"""
        differences = []
        
        try:
            set1 = set(keywords1)
            set2 = set(keywords2)
            
            # 查找缺失的关键词
            missing_in_doc2 = set1 - set2
            missing_in_doc1 = set2 - set1
            
            for keyword in missing_in_doc2:
                severity = "critical" if keyword in self.critical_keywords else "major"
                differences.append({
                    "type": "keyword_missing",
                    "category": "content",
                    "description": f"关键词差异: {keyword}在文档2中缺失",
                    "document": "document2",
                    "content": keyword,
                    "severity": severity
                })
            
            for keyword in missing_in_doc1:
                severity = "critical" if keyword in self.critical_keywords else "major"
                differences.append({
                    "type": "keyword_added",
                    "category": "content",
                    "description": f"关键词差异: {keyword}在文档1中缺失",
                    "document": "document1",
                    "content": keyword,
                    "severity": severity
                })
            
            return differences
            
        except Exception as e:
            logger.error(f"比较关键词失败: {e}")
            return []
    
    def _analyze_layout_differences(self, ocr_result1: OCRResult, ocr_result2: OCRResult, 
                                  layout_comparison: Dict) -> List[Dict]:
        """分析布局差异"""
        differences = []
        
        try:
            # 分析文本块数量差异
            block_count1 = len(ocr_result1.text_blocks)
            block_count2 = len(ocr_result2.text_blocks)
            
            if block_count1 != block_count2:
                differences.append({
                    "type": "block_count_difference",
                    "category": "layout",
                    "description": f"文本块数量差异: 文档1有{block_count1}个，文档2有{block_count2}个",
                    "severity": "minor" if abs(block_count1 - block_count2) <= 2 else "major"
                })
            
            # 分析表格数量差异
            table_count1 = len(ocr_result1.tables)
            table_count2 = len(ocr_result2.tables)
            
            if table_count1 != table_count2:
                differences.append({
                    "type": "table_count_difference",
                    "category": "layout",
                    "description": f"表格数量差异: 文档1有{table_count1}个，文档2有{table_count2}个",
                    "severity": "major"
                })
            
            return differences
            
        except Exception as e:
            logger.error(f"分析布局差异失败: {e}")
            return []
    
    def _analyze_financial_differences(self, ocr_result1: OCRResult, ocr_result2: OCRResult, 
                                     financial_comparison: Dict) -> List[Dict]:
        """分析财务数据差异"""
        differences = []
        
        try:
            # 从财务比对结果中提取差异信息
            amount_comparison = financial_comparison.get("amount_comparison", {})
            
            if amount_comparison.get("unmatched_amounts1", 0) > 0:
                differences.append({
                    "type": "unmatched_amounts",
                    "category": "financial",
                    "description": f"文档1中有{amount_comparison['unmatched_amounts1']}个金额在文档2中未找到匹配",
                    "severity": "critical"
                })
            
            if amount_comparison.get("unmatched_amounts2", 0) > 0:
                differences.append({
                    "type": "unmatched_amounts",
                    "category": "financial",
                    "description": f"文档2中有{amount_comparison['unmatched_amounts2']}个金额在文档1中未找到匹配",
                    "severity": "critical"
                })
            
            return differences
            
        except Exception as e:
            logger.error(f"分析财务数据差异失败: {e}")
            return []
    
    def _analyze_semantic_differences(self, semantic_comparison: Dict) -> List[Dict]:
        """分析语义差异"""
        differences = []
        
        try:
            # 从语义比对结果中提取差异
            if semantic_comparison.get("success", False):
                llm_analysis = semantic_comparison.get("llm_analysis", {})
                
                if llm_analysis.get("success", False):
                    semantic_differences = llm_analysis.get("differences", [])
                    
                    for diff in semantic_differences:
                        differences.append({
                            "type": "semantic_difference",
                            "category": "semantic",
                            "description": diff.get("description", "语义差异"),
                            "severity": diff.get("severity", "minor")
                        })
            
            return differences
            
        except Exception as e:
            logger.error(f"分析语义差异失败: {e}")
            return []
    
    def _categorize_differences(self, differences: List[Dict]) -> Dict:
        """按严重程度分类差异"""
        try:
            categorized = {
                "critical": [],
                "major": [],
                "minor": []
            }
            
            for diff in differences:
                severity = diff.get("severity", "minor")
                if severity in categorized:
                    categorized[severity].append(diff)
                else:
                    categorized["minor"].append(diff)
            
            return categorized
            
        except Exception as e:
            logger.error(f"分类差异失败: {e}")
            return {"critical": [], "major": [], "minor": []}
    
    def _generate_difference_summary(self, categorized_differences: Dict) -> Dict:
        """生成差异摘要"""
        try:
            summary = {
                "total_critical": len(categorized_differences["critical"]),
                "total_major": len(categorized_differences["major"]),
                "total_minor": len(categorized_differences["minor"]),
                "categories": {},
                "most_common_types": {}
            }
            
            # 统计类别分布
            all_differences = (
                categorized_differences["critical"] + 
                categorized_differences["major"] + 
                categorized_differences["minor"]
            )
            
            for diff in all_differences:
                category = diff.get("category", "unknown")
                summary["categories"][category] = summary["categories"].get(category, 0) + 1
                
                diff_type = diff.get("type", "unknown")
                summary["most_common_types"][diff_type] = summary["most_common_types"].get(diff_type, 0) + 1
            
            return summary
            
        except Exception as e:
            logger.error(f"生成差异摘要失败: {e}")
            return {"total_critical": 0, "total_major": 0, "total_minor": 0}
