"""
文本相似度计算器
"""
import re
import math
from typing import List, Dict, Tuple, Set, Optional
from collections import Counter
import difflib
from loguru import logger

try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    logger.warning("jieba未安装，中文分词功能将不可用")
    JIEBA_AVAILABLE = False


class TextSimilarityCalculator:
    """文本相似度计算器"""
    
    def __init__(self):
        self.stop_words = self._load_stop_words()
        if JIEBA_AVAILABLE:
            jieba.setLogLevel(60)  # 关闭jieba日志
    
    def calculate_similarity(self, text1: str, text2: str, method: str = "comprehensive") -> Dict:
        """
        计算文本相似度
        
        Args:
            text1: 第一个文本
            text2: 第二个文本
            method: 计算方法 ("jaccard", "cosine", "edit_distance", "comprehensive")
            
        Returns:
            相似度结果字典
        """
        try:
            if not text1 or not text2:
                return {
                    "similarity": 0.0,
                    "method": method,
                    "details": {"error": "空文本"}
                }
            
            # 预处理文本
            processed_text1 = self._preprocess_text(text1)
            processed_text2 = self._preprocess_text(text2)
            
            if method == "jaccard":
                return self._jaccard_similarity(processed_text1, processed_text2)
            elif method == "cosine":
                return self._cosine_similarity(processed_text1, processed_text2)
            elif method == "edit_distance":
                return self._edit_distance_similarity(processed_text1, processed_text2)
            elif method == "comprehensive":
                return self._comprehensive_similarity(text1, text2, processed_text1, processed_text2)
            else:
                raise ValueError(f"未知的相似度计算方法: {method}")
                
        except Exception as e:
            logger.error(f"计算文本相似度失败: {e}")
            return {
                "similarity": 0.0,
                "method": method,
                "details": {"error": str(e)}
            }
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        try:
            # 转换为小写
            text = text.lower()
            
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            
            # 移除标点符号（保留中文标点的语义）
            text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
            
            # 再次清理空白
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
            
        except Exception as e:
            logger.error(f"文本预处理失败: {e}")
            return text
    
    def _tokenize_text(self, text: str) -> List[str]:
        """文本分词"""
        try:
            if JIEBA_AVAILABLE:
                # 使用jieba进行中文分词
                tokens = list(jieba.cut(text))
            else:
                # 简单的空格分词
                tokens = text.split()
            
            # 过滤停用词和短词
            filtered_tokens = [
                token for token in tokens 
                if len(token) > 1 and token not in self.stop_words
            ]
            
            return filtered_tokens
            
        except Exception as e:
            logger.error(f"文本分词失败: {e}")
            return text.split()
    
    def _jaccard_similarity(self, text1: str, text2: str) -> Dict:
        """Jaccard相似度"""
        try:
            tokens1 = set(self._tokenize_text(text1))
            tokens2 = set(self._tokenize_text(text2))
            
            intersection = tokens1.intersection(tokens2)
            union = tokens1.union(tokens2)
            
            if len(union) == 0:
                similarity = 1.0 if len(tokens1) == 0 and len(tokens2) == 0 else 0.0
            else:
                similarity = len(intersection) / len(union)
            
            return {
                "similarity": similarity,
                "method": "jaccard",
                "details": {
                    "tokens1_count": len(tokens1),
                    "tokens2_count": len(tokens2),
                    "intersection_count": len(intersection),
                    "union_count": len(union),
                    "common_tokens": list(intersection)
                }
            }
            
        except Exception as e:
            logger.error(f"Jaccard相似度计算失败: {e}")
            return {"similarity": 0.0, "method": "jaccard", "details": {"error": str(e)}}
    
    def _cosine_similarity(self, text1: str, text2: str) -> Dict:
        """余弦相似度"""
        try:
            tokens1 = self._tokenize_text(text1)
            tokens2 = self._tokenize_text(text2)
            
            # 构建词频向量
            all_tokens = set(tokens1 + tokens2)
            
            if not all_tokens:
                return {"similarity": 1.0, "method": "cosine", "details": {"empty_texts": True}}
            
            vector1 = [tokens1.count(token) for token in all_tokens]
            vector2 = [tokens2.count(token) for token in all_tokens]
            
            # 计算余弦相似度
            dot_product = sum(a * b for a, b in zip(vector1, vector2))
            magnitude1 = math.sqrt(sum(a * a for a in vector1))
            magnitude2 = math.sqrt(sum(b * b for b in vector2))
            
            if magnitude1 == 0 or magnitude2 == 0:
                similarity = 0.0
            else:
                similarity = dot_product / (magnitude1 * magnitude2)
            
            return {
                "similarity": similarity,
                "method": "cosine",
                "details": {
                    "vector_dimension": len(all_tokens),
                    "dot_product": dot_product,
                    "magnitude1": magnitude1,
                    "magnitude2": magnitude2
                }
            }
            
        except Exception as e:
            logger.error(f"余弦相似度计算失败: {e}")
            return {"similarity": 0.0, "method": "cosine", "details": {"error": str(e)}}
    
    def _edit_distance_similarity(self, text1: str, text2: str) -> Dict:
        """编辑距离相似度"""
        try:
            # 使用difflib计算序列匹配度
            matcher = difflib.SequenceMatcher(None, text1, text2)
            similarity = matcher.ratio()
            
            # 计算Levenshtein距离
            edit_distance = self._levenshtein_distance(text1, text2)
            max_length = max(len(text1), len(text2))
            
            # 基于编辑距离的相似度
            if max_length == 0:
                edit_similarity = 1.0
            else:
                edit_similarity = 1.0 - (edit_distance / max_length)
            
            # 综合两种计算方法
            combined_similarity = (similarity + edit_similarity) / 2
            
            return {
                "similarity": combined_similarity,
                "method": "edit_distance",
                "details": {
                    "sequence_matcher_ratio": similarity,
                    "edit_distance": edit_distance,
                    "edit_similarity": edit_similarity,
                    "text1_length": len(text1),
                    "text2_length": len(text2),
                    "max_length": max_length
                }
            }
            
        except Exception as e:
            logger.error(f"编辑距离相似度计算失败: {e}")
            return {"similarity": 0.0, "method": "edit_distance", "details": {"error": str(e)}}
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """计算Levenshtein距离"""
        try:
            if len(s1) < len(s2):
                return self._levenshtein_distance(s2, s1)
            
            if len(s2) == 0:
                return len(s1)
            
            previous_row = list(range(len(s2) + 1))
            for i, c1 in enumerate(s1):
                current_row = [i + 1]
                for j, c2 in enumerate(s2):
                    insertions = previous_row[j + 1] + 1
                    deletions = current_row[j] + 1
                    substitutions = previous_row[j] + (c1 != c2)
                    current_row.append(min(insertions, deletions, substitutions))
                previous_row = current_row
            
            return previous_row[-1]
            
        except Exception as e:
            logger.error(f"Levenshtein距离计算失败: {e}")
            return max(len(s1), len(s2))
    
    def _comprehensive_similarity(self, original_text1: str, original_text2: str, 
                                processed_text1: str, processed_text2: str) -> Dict:
        """综合相似度计算"""
        try:
            # 计算各种相似度
            jaccard_result = self._jaccard_similarity(processed_text1, processed_text2)
            cosine_result = self._cosine_similarity(processed_text1, processed_text2)
            edit_result = self._edit_distance_similarity(processed_text1, processed_text2)
            
            # 字符级相似度
            char_similarity = self._character_level_similarity(original_text1, original_text2)
            
            # 数字相似度
            number_similarity = self._number_similarity(original_text1, original_text2)
            
            # 结构相似度
            structure_similarity = self._structure_similarity(original_text1, original_text2)
            
            # 权重配置
            weights = {
                "jaccard": 0.2,
                "cosine": 0.25,
                "edit_distance": 0.2,
                "character": 0.15,
                "number": 0.1,
                "structure": 0.1
            }
            
            # 计算加权平均相似度
            weighted_similarity = (
                jaccard_result["similarity"] * weights["jaccard"] +
                cosine_result["similarity"] * weights["cosine"] +
                edit_result["similarity"] * weights["edit_distance"] +
                char_similarity * weights["character"] +
                number_similarity * weights["number"] +
                structure_similarity * weights["structure"]
            )
            
            return {
                "similarity": weighted_similarity,
                "method": "comprehensive",
                "details": {
                    "jaccard": jaccard_result,
                    "cosine": cosine_result,
                    "edit_distance": edit_result,
                    "character_similarity": char_similarity,
                    "number_similarity": number_similarity,
                    "structure_similarity": structure_similarity,
                    "weights": weights
                }
            }
            
        except Exception as e:
            logger.error(f"综合相似度计算失败: {e}")
            return {"similarity": 0.0, "method": "comprehensive", "details": {"error": str(e)}}
    
    def _character_level_similarity(self, text1: str, text2: str) -> float:
        """字符级相似度"""
        try:
            if not text1 and not text2:
                return 1.0
            if not text1 or not text2:
                return 0.0
            
            # 使用difflib计算字符级相似度
            matcher = difflib.SequenceMatcher(None, text1, text2)
            return matcher.ratio()
            
        except Exception as e:
            logger.error(f"字符级相似度计算失败: {e}")
            return 0.0
    
    def _number_similarity(self, text1: str, text2: str) -> float:
        """数字相似度"""
        try:
            # 提取数字
            numbers1 = re.findall(r'\d+\.?\d*', text1)
            numbers2 = re.findall(r'\d+\.?\d*', text2)
            
            if not numbers1 and not numbers2:
                return 1.0
            if not numbers1 or not numbers2:
                return 0.0
            
            # 转换为浮点数
            nums1 = [float(n) for n in numbers1]
            nums2 = [float(n) for n in numbers2]
            
            # 计算数字集合的Jaccard相似度
            set1 = set(nums1)
            set2 = set(nums2)
            
            intersection = set1.intersection(set2)
            union = set1.union(set2)
            
            if len(union) == 0:
                return 1.0
            
            return len(intersection) / len(union)
            
        except Exception as e:
            logger.error(f"数字相似度计算失败: {e}")
            return 0.0
    
    def _structure_similarity(self, text1: str, text2: str) -> float:
        """结构相似度"""
        try:
            # 计算行数相似度
            lines1 = text1.split('\n')
            lines2 = text2.split('\n')
            
            line_count_similarity = 1.0 - abs(len(lines1) - len(lines2)) / max(len(lines1), len(lines2), 1)
            
            # 计算平均行长度相似度
            avg_len1 = sum(len(line) for line in lines1) / len(lines1) if lines1 else 0
            avg_len2 = sum(len(line) for line in lines2) / len(lines2) if lines2 else 0
            
            if avg_len1 == 0 and avg_len2 == 0:
                length_similarity = 1.0
            else:
                length_similarity = 1.0 - abs(avg_len1 - avg_len2) / max(avg_len1, avg_len2, 1)
            
            # 综合结构相似度
            structure_similarity = (line_count_similarity + length_similarity) / 2
            
            return structure_similarity
            
        except Exception as e:
            logger.error(f"结构相似度计算失败: {e}")
            return 0.0
    
    def _load_stop_words(self) -> Set[str]:
        """加载停用词"""
        try:
            # 基本的中英文停用词
            stop_words = {
                # 英文停用词
                'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
                'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
                'to', 'was', 'will', 'with', 'the', 'this', 'but', 'they', 'have',
                'had', 'what', 'said', 'each', 'which', 'their', 'time', 'if',
                
                # 中文停用词
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一',
                '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有',
                '看', '好', '自己', '这', '那', '里', '就是', '还', '把', '来', '给',
                '可以', '又', '为', '已经', '因为', '所以', '但是', '如果', '或者'
            }
            
            return stop_words
            
        except Exception as e:
            logger.error(f"加载停用词失败: {e}")
            return set()
    
    def get_text_statistics(self, text: str) -> Dict:
        """获取文本统计信息"""
        try:
            tokens = self._tokenize_text(text)
            
            return {
                "character_count": len(text),
                "word_count": len(tokens),
                "unique_word_count": len(set(tokens)),
                "line_count": len(text.split('\n')),
                "average_word_length": sum(len(token) for token in tokens) / len(tokens) if tokens else 0,
                "vocabulary_richness": len(set(tokens)) / len(tokens) if tokens else 0
            }
            
        except Exception as e:
            logger.error(f"获取文本统计信息失败: {e}")
            return {}
    
    def find_common_phrases(self, text1: str, text2: str, min_length: int = 3) -> List[str]:
        """查找共同短语"""
        try:
            tokens1 = self._tokenize_text(text1)
            tokens2 = self._tokenize_text(text2)
            
            common_phrases = []
            
            # 查找共同的n-gram
            for n in range(min_length, min(len(tokens1), len(tokens2)) + 1):
                ngrams1 = set(' '.join(tokens1[i:i+n]) for i in range(len(tokens1) - n + 1))
                ngrams2 = set(' '.join(tokens2[i:i+n]) for i in range(len(tokens2) - n + 1))
                
                common_ngrams = ngrams1.intersection(ngrams2)
                common_phrases.extend(common_ngrams)
            
            # 按长度排序，长的在前
            common_phrases.sort(key=len, reverse=True)
            
            return common_phrases
            
        except Exception as e:
            logger.error(f"查找共同短语失败: {e}")
            return []
