"""
综合比对引擎
"""
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from loguru import logger

from src.offerDiff.models.ocr_result import OCRResult
from src.offerDiff.comparison.text_similarity import TextSimilarityCalculator
from src.offerDiff.comparison.layout_comparison import LayoutComparator
from src.offerDiff.comparison.financial_comparison import FinancialComparator
from src.offerDiff.comparison.semantic_comparison import SemanticComparator
from src.offerDiff.comparison.difference_analyzer import DifferenceAnalyzer


class ComparisonEngine:
    """综合比对引擎"""
    
    def __init__(self):
        self.text_similarity = TextSimilarityCalculator()
        self.layout_comparator = LayoutComparator()
        self.financial_comparator = FinancialComparator()
        self.semantic_comparator = SemanticComparator()
        self.difference_analyzer = DifferenceAnalyzer()
        
        # 权重配置
        self.weights = {
            "text_similarity": 0.25,
            "layout_similarity": 0.20,
            "financial_consistency": 0.30,
            "semantic_similarity": 0.25
        }
        
        # 阈值配置
        self.thresholds = {
            "high_similarity": 0.9,
            "medium_similarity": 0.7,
            "low_similarity": 0.5,
            "critical_difference": 0.3
        }
    
    async def compare_documents(self, ocr_result1: OCRResult, ocr_result2: OCRResult, 
                              comparison_options: Optional[Dict] = None) -> Dict:
        """
        综合比对两个文档
        
        Args:
            ocr_result1: 第一个OCR结果
            ocr_result2: 第二个OCR结果
            comparison_options: 比对选项
            
        Returns:
            综合比对结果
        """
        try:
            start_time = datetime.utcnow()
            
            # 应用比对选项
            if comparison_options:
                self._apply_comparison_options(comparison_options)
            
            # 并行执行各种比对
            comparison_tasks = [
                self._perform_text_comparison(ocr_result1, ocr_result2),
                self._perform_layout_comparison(ocr_result1, ocr_result2),
                self._perform_financial_comparison(ocr_result1, ocr_result2),
                self._perform_semantic_comparison(ocr_result1, ocr_result2)
            ]
            
            # 等待所有比对完成
            comparison_results = await asyncio.gather(*comparison_tasks, return_exceptions=True)
            
            # 处理比对结果
            text_comparison = self._handle_result(comparison_results[0], "text_comparison")
            layout_comparison = self._handle_result(comparison_results[1], "layout_comparison")
            financial_comparison = self._handle_result(comparison_results[2], "financial_comparison")
            semantic_comparison = self._handle_result(comparison_results[3], "semantic_comparison")
            
            # 计算综合相似度
            overall_similarity = self._calculate_overall_similarity(
                text_comparison, layout_comparison, financial_comparison, semantic_comparison
            )
            
            # 分析差异
            differences = await self._analyze_differences(
                ocr_result1, ocr_result2, text_comparison, layout_comparison, 
                financial_comparison, semantic_comparison
            )
            
            # 生成比对报告
            comparison_report = self._generate_comparison_report(
                overall_similarity, text_comparison, layout_comparison,
                financial_comparison, semantic_comparison, differences
            )
            
            # 风险评估
            risk_assessment = self._assess_risks(differences, overall_similarity)
            
            # 生成建议
            recommendations = self._generate_recommendations(
                overall_similarity, differences, risk_assessment
            )
            
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            result = {
                "comparison_id": f"comp_{int(start_time.timestamp())}",
                "timestamp": start_time.isoformat(),
                "processing_time": processing_time,
                "overall_similarity": overall_similarity,
                "similarity_level": self._classify_similarity_level(overall_similarity),
                "text_comparison": text_comparison,
                "layout_comparison": layout_comparison,
                "financial_comparison": financial_comparison,
                "semantic_comparison": semantic_comparison,
                "differences": differences,
                "risk_assessment": risk_assessment,
                "recommendations": recommendations,
                "comparison_report": comparison_report,
                "metadata": {
                    "document1_id": ocr_result1.id,
                    "document2_id": ocr_result2.id,
                    "weights_used": self.weights,
                    "thresholds_used": self.thresholds
                }
            }
            
            logger.info(f"文档比对完成: {overall_similarity:.3f} ({processing_time:.2f}s)")
            return result
            
        except Exception as e:
            logger.error(f"文档比对失败: {e}")
            return {
                "comparison_id": f"comp_error_{int(datetime.utcnow().timestamp())}",
                "timestamp": datetime.utcnow().isoformat(),
                "overall_similarity": 0.0,
                "error": str(e),
                "success": False
            }
    
    def _apply_comparison_options(self, options: Dict):
        """应用比对选项"""
        try:
            # 更新权重
            if "weights" in options:
                self.weights.update(options["weights"])
            
            # 更新阈值
            if "thresholds" in options:
                self.thresholds.update(options["thresholds"])
            
            # 更新比对器配置
            if "text_options" in options:
                # 配置文本比对选项
                pass
            
            if "layout_options" in options:
                # 配置布局比对选项
                layout_opts = options["layout_options"]
                if "position_tolerance" in layout_opts:
                    self.layout_comparator.position_tolerance = layout_opts["position_tolerance"]
                if "size_tolerance" in layout_opts:
                    self.layout_comparator.size_tolerance = layout_opts["size_tolerance"]
            
            if "financial_options" in options:
                # 配置财务比对选项
                financial_opts = options["financial_options"]
                if "amount_tolerance" in financial_opts:
                    self.financial_comparator.amount_tolerance = financial_opts["amount_tolerance"]
                if "percentage_tolerance" in financial_opts:
                    self.financial_comparator.percentage_tolerance = financial_opts["percentage_tolerance"]
            
            if "semantic_options" in options:
                # 配置语义比对选项
                semantic_opts = options["semantic_options"]
                if "similarity_threshold" in semantic_opts:
                    self.semantic_comparator.similarity_threshold = semantic_opts["similarity_threshold"]
                if "confidence_threshold" in semantic_opts:
                    self.semantic_comparator.confidence_threshold = semantic_opts["confidence_threshold"]
            
        except Exception as e:
            logger.error(f"应用比对选项失败: {e}")
    
    async def _perform_text_comparison(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """执行文本比对"""
        try:
            # 提取全文
            text1 = ocr_result1.full_text
            text2 = ocr_result2.full_text
            
            # 计算文本相似度
            similarity_result = self.text_similarity.calculate_similarity(text1, text2, "comprehensive")
            
            # 查找共同短语
            common_phrases = self.text_similarity.find_common_phrases(text1, text2)
            
            # 获取文本统计
            stats1 = self.text_similarity.get_text_statistics(text1)
            stats2 = self.text_similarity.get_text_statistics(text2)
            
            return {
                "similarity": similarity_result["similarity"],
                "method": similarity_result["method"],
                "details": similarity_result["details"],
                "common_phrases": common_phrases[:10],  # 限制数量
                "text_statistics": {
                    "document1": stats1,
                    "document2": stats2
                },
                "success": True
            }
            
        except Exception as e:
            logger.error(f"文本比对失败: {e}")
            return {"similarity": 0.0, "success": False, "error": str(e)}
    
    async def _perform_layout_comparison(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """执行布局比对"""
        try:
            layout_result = self.layout_comparator.compare_layouts(ocr_result1, ocr_result2)
            layout_result["success"] = True
            return layout_result
            
        except Exception as e:
            logger.error(f"布局比对失败: {e}")
            return {"overall_similarity": 0.0, "success": False, "error": str(e)}
    
    async def _perform_financial_comparison(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """执行财务比对"""
        try:
            financial_result = self.financial_comparator.compare_financial_data(ocr_result1, ocr_result2)
            financial_result["success"] = True
            return financial_result
            
        except Exception as e:
            logger.error(f"财务比对失败: {e}")
            return {"overall_consistency": 0.0, "success": False, "error": str(e)}
    
    async def _perform_semantic_comparison(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """执行语义比对"""
        try:
            semantic_result = await self.semantic_comparator.compare_semantically(ocr_result1, ocr_result2)
            semantic_result["success"] = True
            return semantic_result
            
        except Exception as e:
            logger.error(f"语义比对失败: {e}")
            return {"overall_similarity": 0.0, "success": False, "error": str(e)}
    
    def _handle_result(self, result: Any, comparison_type: str) -> Dict:
        """处理比对结果"""
        if isinstance(result, Exception):
            logger.error(f"{comparison_type}比对异常: {result}")
            return {"success": False, "error": str(result)}
        elif isinstance(result, dict) and result.get("success", True):
            return result
        else:
            logger.warning(f"{comparison_type}比对结果异常")
            return {"success": False, "error": "未知错误"}
    
    def _calculate_overall_similarity(self, text_comparison: Dict, layout_comparison: Dict,
                                    financial_comparison: Dict, semantic_comparison: Dict) -> float:
        """计算综合相似度"""
        try:
            # 提取各项相似度分数
            text_score = text_comparison.get("similarity", 0.0) if text_comparison.get("success", False) else 0.0
            layout_score = layout_comparison.get("overall_similarity", 0.0) if layout_comparison.get("success", False) else 0.0
            financial_score = financial_comparison.get("overall_consistency", 0.0) if financial_comparison.get("success", False) else 0.0
            semantic_score = semantic_comparison.get("overall_similarity", 0.0) if semantic_comparison.get("success", False) else 0.0
            
            # 加权计算
            overall_similarity = (
                text_score * self.weights["text_similarity"] +
                layout_score * self.weights["layout_similarity"] +
                financial_score * self.weights["financial_consistency"] +
                semantic_score * self.weights["semantic_similarity"]
            )
            
            return min(1.0, max(0.0, overall_similarity))
            
        except Exception as e:
            logger.error(f"计算综合相似度失败: {e}")
            return 0.0
    
    def _classify_similarity_level(self, similarity: float) -> str:
        """分类相似度等级"""
        if similarity >= self.thresholds["high_similarity"]:
            return "high"
        elif similarity >= self.thresholds["medium_similarity"]:
            return "medium"
        elif similarity >= self.thresholds["low_similarity"]:
            return "low"
        else:
            return "very_low"
    
    async def _analyze_differences(self, ocr_result1: OCRResult, ocr_result2: OCRResult,
                                 text_comparison: Dict, layout_comparison: Dict,
                                 financial_comparison: Dict, semantic_comparison: Dict) -> Dict:
        """分析差异"""
        try:
            differences = await self.difference_analyzer.analyze_differences(
                ocr_result1, ocr_result2, {
                    "text_comparison": text_comparison,
                    "layout_comparison": layout_comparison,
                    "financial_comparison": financial_comparison,
                    "semantic_comparison": semantic_comparison
                }
            )
            
            return differences
            
        except Exception as e:
            logger.error(f"分析差异失败: {e}")
            return {"critical_differences": [], "major_differences": [], "minor_differences": []}
    
    def _generate_comparison_report(self, overall_similarity: float, text_comparison: Dict,
                                  layout_comparison: Dict, financial_comparison: Dict,
                                  semantic_comparison: Dict, differences: Dict) -> Dict:
        """生成比对报告"""
        try:
            report = {
                "summary": {
                    "overall_similarity": overall_similarity,
                    "similarity_level": self._classify_similarity_level(overall_similarity),
                    "total_differences": len(differences.get("critical_differences", [])) +
                                       len(differences.get("major_differences", [])) +
                                       len(differences.get("minor_differences", []))
                },
                "detailed_scores": {
                    "text_similarity": text_comparison.get("similarity", 0.0),
                    "layout_similarity": layout_comparison.get("overall_similarity", 0.0),
                    "financial_consistency": financial_comparison.get("overall_consistency", 0.0),
                    "semantic_similarity": semantic_comparison.get("overall_similarity", 0.0)
                },
                "key_findings": self._extract_key_findings(
                    text_comparison, layout_comparison, financial_comparison, semantic_comparison
                ),
                "difference_summary": {
                    "critical": len(differences.get("critical_differences", [])),
                    "major": len(differences.get("major_differences", [])),
                    "minor": len(differences.get("minor_differences", []))
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成比对报告失败: {e}")
            return {"summary": {"overall_similarity": overall_similarity}}
    
    def _extract_key_findings(self, text_comparison: Dict, layout_comparison: Dict,
                            financial_comparison: Dict, semantic_comparison: Dict) -> List[str]:
        """提取关键发现"""
        findings = []
        
        try:
            # 文本相似度发现
            text_sim = text_comparison.get("similarity", 0.0)
            if text_sim > 0.9:
                findings.append("文本内容高度相似")
            elif text_sim < 0.5:
                findings.append("文本内容存在显著差异")
            
            # 布局相似度发现
            layout_sim = layout_comparison.get("overall_similarity", 0.0)
            if layout_sim > 0.9:
                findings.append("文档布局高度一致")
            elif layout_sim < 0.5:
                findings.append("文档布局存在明显差异")
            
            # 财务一致性发现
            financial_consistency = financial_comparison.get("overall_consistency", 0.0)
            if financial_consistency > 0.9:
                findings.append("财务数据完全一致")
            elif financial_consistency < 0.7:
                findings.append("财务数据存在不一致")
            
            # 语义相似度发现
            semantic_sim = semantic_comparison.get("overall_similarity", 0.0)
            if semantic_sim > 0.9:
                findings.append("语义内容高度匹配")
            elif semantic_sim < 0.6:
                findings.append("语义内容存在差异")
            
            return findings
            
        except Exception as e:
            logger.error(f"提取关键发现失败: {e}")
            return []
