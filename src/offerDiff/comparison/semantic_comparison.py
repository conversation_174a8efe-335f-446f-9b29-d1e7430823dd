"""
语义比对引擎
"""
from typing import Dict, List, Optional, Any
from loguru import logger

from src.offerDiff.models.ocr_result import OCRResult
from src.offerDiff.comparison.text_similarity import TextSimilarityCalculator
from src.offerDiff.llm.api_router import LLMAPIRouter
from src.offerDiff.llm.result_parser import LLMResultParser


class SemanticComparator:
    """语义比对器"""
    
    def __init__(self):
        self.text_similarity = TextSimilarityCalculator()
        self.llm_router = LLMAPIRouter()
        self.result_parser = LLMResultParser()
        self.similarity_threshold = 0.8
        self.confidence_threshold = 0.7
    
    def compare_semantically(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """
        语义比对两个OCR结果
        
        Args:
            ocr_result1: 第一个OCR结果
            ocr_result2: 第二个OCR结果
            
        Returns:
            语义比对结果
        """
        try:
            # 提取可比对的内容
            content1 = self._extract_comparable_content(ocr_result1)
            content2 = self._extract_comparable_content(ocr_result2)
            
            # 基础文本相似度计算
            text_similarity = self._calculate_text_similarity(content1, content2)
            
            # 使用LLM进行深度语义分析
            llm_analysis = self._perform_llm_semantic_analysis(content1, content2)
            
            # 结构化内容比对
            structure_comparison = self._compare_content_structure(content1, content2)
            
            # 关键信息比对
            key_info_comparison = self._compare_key_information(content1, content2)
            
            # 上下文一致性分析
            context_analysis = self._analyze_context_consistency(content1, content2)
            
            # 综合语义相似度
            overall_similarity = self._calculate_overall_semantic_similarity(
                text_similarity, llm_analysis, structure_comparison, 
                key_info_comparison, context_analysis
            )
            
            # 生成差异报告
            differences = self._generate_difference_report(
                content1, content2, llm_analysis
            )
            
            result = {
                "overall_similarity": overall_similarity,
                "text_similarity": text_similarity,
                "llm_analysis": llm_analysis,
                "structure_comparison": structure_comparison,
                "key_info_comparison": key_info_comparison,
                "context_analysis": context_analysis,
                "differences": differences,
                "recommendation": self._generate_recommendation(overall_similarity, differences)
            }
            
            logger.info(f"语义比对完成，相似度: {overall_similarity:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"语义比对失败: {e}")
            return {
                "overall_similarity": 0.0,
                "error": str(e)
            }
    
    def _extract_comparable_content(self, ocr_result: OCRResult) -> Dict:
        """提取可比对的内容"""
        try:
            content = {
                "full_text": ocr_result.full_text,
                "text_blocks": [],
                "table_content": [],
                "financial_data": ocr_result.get_financial_data(),
                "structure_info": {
                    "total_text_blocks": ocr_result.total_text_blocks,
                    "total_tables": ocr_result.total_tables,
                    "total_cells": ocr_result.total_cells
                }
            }
            
            # 提取文本块内容
            for block in ocr_result.text_blocks:
                if block.is_valid and block.confidence > self.confidence_threshold:
                    content["text_blocks"].append({
                        "text": block.text,
                        "type": block.text_type.value,
                        "confidence": block.confidence,
                        "position": {
                            "x": block.bbox.x1,
                            "y": block.bbox.y1,
                            "width": block.bbox.width,
                            "height": block.bbox.height
                        }
                    })
            
            # 提取表格内容
            for table in ocr_result.tables:
                table_text = []
                for cell in table.cells:
                    if cell.is_valid and cell.confidence > self.confidence_threshold:
                        table_text.append({
                            "text": cell.text,
                            "row": cell.row,
                            "col": cell.col,
                            "type": cell.text_type.value
                        })
                
                if table_text:
                    content["table_content"].append({
                        "table_id": table.id,
                        "rows": table.rows,
                        "columns": table.columns,
                        "cells": table_text
                    })
            
            return content
            
        except Exception as e:
            logger.error(f"提取可比对内容失败: {e}")
            return {"full_text": "", "text_blocks": [], "table_content": [], "financial_data": {}}
    
    def _calculate_text_similarity(self, content1: Dict, content2: Dict) -> Dict:
        """计算文本相似度"""
        try:
            # 全文相似度
            full_text_similarity = self.text_similarity.calculate_similarity(
                content1["full_text"], content2["full_text"], "comprehensive"
            )
            
            # 文本块相似度
            block_similarities = []
            for block1 in content1["text_blocks"]:
                best_similarity = 0.0
                for block2 in content2["text_blocks"]:
                    similarity = self.text_similarity.calculate_similarity(
                        block1["text"], block2["text"], "comprehensive"
                    )
                    if similarity["similarity"] > best_similarity:
                        best_similarity = similarity["similarity"]
                
                block_similarities.append(best_similarity)
            
            avg_block_similarity = sum(block_similarities) / len(block_similarities) if block_similarities else 0.0
            
            # 表格内容相似度
            table_similarities = []
            for table1 in content1["table_content"]:
                table1_text = " ".join([cell["text"] for cell in table1["cells"]])
                best_similarity = 0.0
                
                for table2 in content2["table_content"]:
                    table2_text = " ".join([cell["text"] for cell in table2["cells"]])
                    similarity = self.text_similarity.calculate_similarity(
                        table1_text, table2_text, "comprehensive"
                    )
                    if similarity["similarity"] > best_similarity:
                        best_similarity = similarity["similarity"]
                
                table_similarities.append(best_similarity)
            
            avg_table_similarity = sum(table_similarities) / len(table_similarities) if table_similarities else 0.0
            
            return {
                "full_text_similarity": full_text_similarity,
                "avg_block_similarity": avg_block_similarity,
                "avg_table_similarity": avg_table_similarity,
                "block_similarities": block_similarities,
                "table_similarities": table_similarities
            }
            
        except Exception as e:
            logger.error(f"计算文本相似度失败: {e}")
            return {"full_text_similarity": {"similarity": 0.0}, "avg_block_similarity": 0.0, "avg_table_similarity": 0.0}
    
    async def _perform_llm_semantic_analysis(self, content1: Dict, content2: Dict) -> Dict:
        """使用LLM进行语义分析"""
        try:
            # 调用LLM进行语义比对
            llm_result = await self.llm_router.compare_content_semantically(
                content1["full_text"], content2["full_text"]
            )
            
            if llm_result.get("success", False):
                # 解析LLM结果
                parsed_result = self.result_parser.parse_semantic_comparison_result(llm_result)
                
                if parsed_result.get("success", False):
                    return parsed_result
                else:
                    logger.warning("LLM结果解析失败，使用基础分析")
                    return self._fallback_semantic_analysis(content1, content2)
            else:
                logger.warning("LLM语义分析失败，使用基础分析")
                return self._fallback_semantic_analysis(content1, content2)
                
        except Exception as e:
            logger.error(f"LLM语义分析失败: {e}")
            return self._fallback_semantic_analysis(content1, content2)
    
    def _fallback_semantic_analysis(self, content1: Dict, content2: Dict) -> Dict:
        """备用语义分析"""
        try:
            # 基础的语义分析逻辑
            financial_data1 = content1.get("financial_data", {})
            financial_data2 = content2.get("financial_data", {})
            
            # 比较财务数据
            financial_consistency = self._compare_financial_consistency(financial_data1, financial_data2)
            
            # 比较结构信息
            structure1 = content1.get("structure_info", {})
            structure2 = content2.get("structure_info", {})
            structure_similarity = self._compare_structure_similarity(structure1, structure2)
            
            return {
                "success": True,
                "overall_similarity": (financial_consistency + structure_similarity) / 2,
                "financial_consistency": financial_consistency,
                "structure_similarity": structure_similarity,
                "differences": [],
                "confidence": 0.6,  # 基础分析置信度较低
                "method": "fallback"
            }
            
        except Exception as e:
            logger.error(f"备用语义分析失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _compare_financial_consistency(self, financial_data1: Dict, financial_data2: Dict) -> float:
        """比较财务数据一致性"""
        try:
            if not financial_data1 and not financial_data2:
                return 1.0
            
            # 比较货币数据
            currencies1 = set(financial_data1.get("currencies", []))
            currencies2 = set(financial_data2.get("currencies", []))
            
            if currencies1 or currencies2:
                currency_similarity = len(currencies1.intersection(currencies2)) / len(currencies1.union(currencies2))
            else:
                currency_similarity = 1.0
            
            # 比较数字数据
            numbers1 = set(financial_data1.get("numbers", []))
            numbers2 = set(financial_data2.get("numbers", []))
            
            if numbers1 or numbers2:
                number_similarity = len(numbers1.intersection(numbers2)) / len(numbers1.union(numbers2))
            else:
                number_similarity = 1.0
            
            # 比较百分比数据
            percentages1 = set(financial_data1.get("percentages", []))
            percentages2 = set(financial_data2.get("percentages", []))
            
            if percentages1 or percentages2:
                percentage_similarity = len(percentages1.intersection(percentages2)) / len(percentages1.union(percentages2))
            else:
                percentage_similarity = 1.0
            
            # 综合财务一致性
            consistency = (currency_similarity + number_similarity + percentage_similarity) / 3
            
            return consistency
            
        except Exception as e:
            logger.error(f"比较财务数据一致性失败: {e}")
            return 0.0
    
    def _compare_structure_similarity(self, structure1: Dict, structure2: Dict) -> float:
        """比较结构相似性"""
        try:
            # 比较文本块数量
            blocks1 = structure1.get("total_text_blocks", 0)
            blocks2 = structure2.get("total_text_blocks", 0)
            
            if blocks1 == 0 and blocks2 == 0:
                block_similarity = 1.0
            else:
                block_similarity = 1.0 - abs(blocks1 - blocks2) / max(blocks1, blocks2, 1)
            
            # 比较表格数量
            tables1 = structure1.get("total_tables", 0)
            tables2 = structure2.get("total_tables", 0)
            
            if tables1 == 0 and tables2 == 0:
                table_similarity = 1.0
            else:
                table_similarity = 1.0 - abs(tables1 - tables2) / max(tables1, tables2, 1)
            
            # 比较单元格数量
            cells1 = structure1.get("total_cells", 0)
            cells2 = structure2.get("total_cells", 0)
            
            if cells1 == 0 and cells2 == 0:
                cell_similarity = 1.0
            else:
                cell_similarity = 1.0 - abs(cells1 - cells2) / max(cells1, cells2, 1)
            
            # 综合结构相似性
            similarity = (block_similarity + table_similarity + cell_similarity) / 3
            
            return similarity
            
        except Exception as e:
            logger.error(f"比较结构相似性失败: {e}")
            return 0.0
    
    def _compare_content_structure(self, content1: Dict, content2: Dict) -> Dict:
        """比较内容结构"""
        try:
            # 比较文本块结构
            blocks1 = content1.get("text_blocks", [])
            blocks2 = content2.get("text_blocks", [])
            
            block_type_distribution1 = {}
            block_type_distribution2 = {}
            
            for block in blocks1:
                block_type = block.get("type", "text")
                block_type_distribution1[block_type] = block_type_distribution1.get(block_type, 0) + 1
            
            for block in blocks2:
                block_type = block.get("type", "text")
                block_type_distribution2[block_type] = block_type_distribution2.get(block_type, 0) + 1
            
            # 计算类型分布相似度
            all_types = set(block_type_distribution1.keys()) | set(block_type_distribution2.keys())
            type_similarity = 0.0
            
            if all_types:
                intersection = 0
                union = 0
                for block_type in all_types:
                    count1 = block_type_distribution1.get(block_type, 0)
                    count2 = block_type_distribution2.get(block_type, 0)
                    intersection += min(count1, count2)
                    union += max(count1, count2)
                
                type_similarity = intersection / union if union > 0 else 0.0
            
            # 比较表格结构
            tables1 = content1.get("table_content", [])
            tables2 = content2.get("table_content", [])
            
            table_structure_similarity = self._compare_table_structures(tables1, tables2)
            
            return {
                "block_type_similarity": type_similarity,
                "table_structure_similarity": table_structure_similarity,
                "block_count_diff": abs(len(blocks1) - len(blocks2)),
                "table_count_diff": abs(len(tables1) - len(tables2))
            }
            
        except Exception as e:
            logger.error(f"比较内容结构失败: {e}")
            return {"block_type_similarity": 0.0, "table_structure_similarity": 0.0}
    
    def _compare_table_structures(self, tables1: List[Dict], tables2: List[Dict]) -> float:
        """比较表格结构"""
        try:
            if not tables1 and not tables2:
                return 1.0
            if not tables1 or not tables2:
                return 0.0
            
            structure_similarities = []
            
            for table1 in tables1:
                best_similarity = 0.0
                
                for table2 in tables2:
                    # 比较行列数
                    rows_similarity = 1.0 - abs(table1["rows"] - table2["rows"]) / max(table1["rows"], table2["rows"], 1)
                    cols_similarity = 1.0 - abs(table1["columns"] - table2["columns"]) / max(table1["columns"], table2["columns"], 1)
                    
                    # 比较单元格数量
                    cells1_count = len(table1["cells"])
                    cells2_count = len(table2["cells"])
                    cells_similarity = 1.0 - abs(cells1_count - cells2_count) / max(cells1_count, cells2_count, 1)
                    
                    # 综合相似度
                    similarity = (rows_similarity + cols_similarity + cells_similarity) / 3
                    
                    if similarity > best_similarity:
                        best_similarity = similarity
                
                structure_similarities.append(best_similarity)
            
            return sum(structure_similarities) / len(structure_similarities) if structure_similarities else 0.0
            
        except Exception as e:
            logger.error(f"比较表格结构失败: {e}")
            return 0.0
