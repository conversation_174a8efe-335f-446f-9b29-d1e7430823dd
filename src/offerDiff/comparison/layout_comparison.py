"""
布局比对算法
"""
import math
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from loguru import logger

from src.offerDiff.models.ocr_result import OCRResult, TextBlock, Table, BoundingBox


class LayoutComparator:
    """布局比对器"""
    
    def __init__(self):
        self.position_tolerance = 10  # 位置容差（像素）
        self.size_tolerance = 0.1     # 尺寸容差（比例）
        self.alignment_tolerance = 5   # 对齐容差（像素）
    
    def compare_layouts(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """
        比较两个OCR结果的布局
        
        Args:
            ocr_result1: 第一个OCR结果
            ocr_result2: 第二个OCR结果
            
        Returns:
            布局比对结果
        """
        try:
            # 提取布局元素
            elements1 = self._extract_layout_elements(ocr_result1)
            elements2 = self._extract_layout_elements(ocr_result2)
            
            # 计算整体布局相似度
            overall_similarity = self._calculate_overall_layout_similarity(elements1, elements2)
            
            # 比较文本块布局
            text_layout_comparison = self._compare_text_block_layouts(
                ocr_result1.text_blocks, ocr_result2.text_blocks
            )
            
            # 比较表格布局
            table_layout_comparison = self._compare_table_layouts(
                ocr_result1.tables, ocr_result2.tables
            )
            
            # 分析布局差异
            layout_differences = self._analyze_layout_differences(elements1, elements2)
            
            # 计算布局一致性指标
            consistency_metrics = self._calculate_consistency_metrics(elements1, elements2)
            
            result = {
                "overall_similarity": overall_similarity,
                "text_layout": text_layout_comparison,
                "table_layout": table_layout_comparison,
                "differences": layout_differences,
                "consistency_metrics": consistency_metrics,
                "element_count": {
                    "document1": len(elements1),
                    "document2": len(elements2)
                }
            }
            
            logger.info(f"布局比对完成，相似度: {overall_similarity:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"布局比对失败: {e}")
            return {
                "overall_similarity": 0.0,
                "error": str(e)
            }
    
    def _extract_layout_elements(self, ocr_result: OCRResult) -> List[Dict]:
        """提取布局元素"""
        elements = []
        
        try:
            # 提取文本块元素
            for i, text_block in enumerate(ocr_result.text_blocks):
                element = {
                    "type": "text_block",
                    "id": f"text_{i}",
                    "bbox": text_block.bbox,
                    "area": text_block.bbox.area,
                    "center": text_block.bbox.center,
                    "text": text_block.text,
                    "text_type": text_block.text_type.value,
                    "confidence": text_block.confidence
                }
                elements.append(element)
            
            # 提取表格元素
            for i, table in enumerate(ocr_result.tables):
                element = {
                    "type": "table",
                    "id": f"table_{i}",
                    "bbox": table.bbox,
                    "area": table.bbox.area,
                    "center": table.bbox.center,
                    "rows": table.rows,
                    "columns": table.columns,
                    "cell_count": len(table.cells),
                    "confidence": table.confidence
                }
                elements.append(element)
                
                # 提取表格单元格元素
                for j, cell in enumerate(table.cells):
                    cell_element = {
                        "type": "table_cell",
                        "id": f"table_{i}_cell_{j}",
                        "parent_table": f"table_{i}",
                        "bbox": cell.bbox,
                        "area": cell.bbox.area,
                        "center": cell.bbox.center,
                        "row": cell.row,
                        "col": cell.col,
                        "text": cell.text,
                        "text_type": cell.text_type.value,
                        "confidence": cell.confidence
                    }
                    elements.append(cell_element)
            
            return elements
            
        except Exception as e:
            logger.error(f"提取布局元素失败: {e}")
            return []
    
    def _calculate_overall_layout_similarity(self, elements1: List[Dict], elements2: List[Dict]) -> float:
        """计算整体布局相似度"""
        try:
            if not elements1 and not elements2:
                return 1.0
            if not elements1 or not elements2:
                return 0.0
            
            # 计算位置分布相似度
            position_similarity = self._calculate_position_distribution_similarity(elements1, elements2)
            
            # 计算尺寸分布相似度
            size_similarity = self._calculate_size_distribution_similarity(elements1, elements2)
            
            # 计算元素类型分布相似度
            type_similarity = self._calculate_type_distribution_similarity(elements1, elements2)
            
            # 计算空间关系相似度
            spatial_similarity = self._calculate_spatial_relationship_similarity(elements1, elements2)
            
            # 加权平均
            weights = {
                "position": 0.3,
                "size": 0.2,
                "type": 0.2,
                "spatial": 0.3
            }
            
            overall_similarity = (
                position_similarity * weights["position"] +
                size_similarity * weights["size"] +
                type_similarity * weights["type"] +
                spatial_similarity * weights["spatial"]
            )
            
            return overall_similarity
            
        except Exception as e:
            logger.error(f"计算整体布局相似度失败: {e}")
            return 0.0
    
    def _calculate_position_distribution_similarity(self, elements1: List[Dict], elements2: List[Dict]) -> float:
        """计算位置分布相似度"""
        try:
            # 提取中心点坐标
            centers1 = [elem["center"] for elem in elements1]
            centers2 = [elem["center"] for elem in elements2]
            
            if not centers1 or not centers2:
                return 0.0
            
            # 计算位置分布的统计特征
            x_coords1 = [c[0] for c in centers1]
            y_coords1 = [c[1] for c in centers1]
            x_coords2 = [c[0] for c in centers2]
            y_coords2 = [c[1] for c in centers2]
            
            # 计算分布的均值和标准差
            x_mean1, x_std1 = np.mean(x_coords1), np.std(x_coords1)
            y_mean1, y_std1 = np.mean(y_coords1), np.std(y_coords1)
            x_mean2, x_std2 = np.mean(x_coords2), np.std(x_coords2)
            y_mean2, y_std2 = np.mean(y_coords2), np.std(y_coords2)
            
            # 计算均值差异的相似度
            x_mean_similarity = 1.0 - min(1.0, abs(x_mean1 - x_mean2) / max(x_mean1, x_mean2, 1))
            y_mean_similarity = 1.0 - min(1.0, abs(y_mean1 - y_mean2) / max(y_mean1, y_mean2, 1))
            
            # 计算标准差差异的相似度
            x_std_similarity = 1.0 - min(1.0, abs(x_std1 - x_std2) / max(x_std1, x_std2, 1))
            y_std_similarity = 1.0 - min(1.0, abs(y_std1 - y_std2) / max(y_std1, y_std2, 1))
            
            # 综合位置分布相似度
            position_similarity = (x_mean_similarity + y_mean_similarity + x_std_similarity + y_std_similarity) / 4
            
            return position_similarity
            
        except Exception as e:
            logger.error(f"计算位置分布相似度失败: {e}")
            return 0.0
    
    def _calculate_size_distribution_similarity(self, elements1: List[Dict], elements2: List[Dict]) -> float:
        """计算尺寸分布相似度"""
        try:
            areas1 = [elem["area"] for elem in elements1]
            areas2 = [elem["area"] for elem in elements2]
            
            if not areas1 or not areas2:
                return 0.0
            
            # 计算面积分布的统计特征
            mean1, std1 = np.mean(areas1), np.std(areas1)
            mean2, std2 = np.mean(areas2), np.std(areas2)
            
            # 计算相似度
            mean_similarity = 1.0 - min(1.0, abs(mean1 - mean2) / max(mean1, mean2, 1))
            std_similarity = 1.0 - min(1.0, abs(std1 - std2) / max(std1, std2, 1))
            
            return (mean_similarity + std_similarity) / 2
            
        except Exception as e:
            logger.error(f"计算尺寸分布相似度失败: {e}")
            return 0.0
    
    def _calculate_type_distribution_similarity(self, elements1: List[Dict], elements2: List[Dict]) -> float:
        """计算元素类型分布相似度"""
        try:
            # 统计元素类型
            types1 = {}
            types2 = {}
            
            for elem in elements1:
                elem_type = elem["type"]
                types1[elem_type] = types1.get(elem_type, 0) + 1
            
            for elem in elements2:
                elem_type = elem["type"]
                types2[elem_type] = types2.get(elem_type, 0) + 1
            
            # 计算类型分布的Jaccard相似度
            all_types = set(types1.keys()) | set(types2.keys())
            
            if not all_types:
                return 1.0
            
            intersection = 0
            union = 0
            
            for elem_type in all_types:
                count1 = types1.get(elem_type, 0)
                count2 = types2.get(elem_type, 0)
                intersection += min(count1, count2)
                union += max(count1, count2)
            
            return intersection / union if union > 0 else 0.0
            
        except Exception as e:
            logger.error(f"计算类型分布相似度失败: {e}")
            return 0.0
    
    def _calculate_spatial_relationship_similarity(self, elements1: List[Dict], elements2: List[Dict]) -> float:
        """计算空间关系相似度"""
        try:
            # 计算元素间的相对位置关系
            relationships1 = self._extract_spatial_relationships(elements1)
            relationships2 = self._extract_spatial_relationships(elements2)
            
            if not relationships1 and not relationships2:
                return 1.0
            if not relationships1 or not relationships2:
                return 0.0
            
            # 计算关系的相似度
            common_relationships = 0
            total_relationships = len(relationships1) + len(relationships2)
            
            for rel1 in relationships1:
                for rel2 in relationships2:
                    if self._are_relationships_similar(rel1, rel2):
                        common_relationships += 1
                        break
            
            similarity = (2 * common_relationships) / total_relationships if total_relationships > 0 else 0.0
            
            return similarity
            
        except Exception as e:
            logger.error(f"计算空间关系相似度失败: {e}")
            return 0.0
    
    def _extract_spatial_relationships(self, elements: List[Dict]) -> List[Dict]:
        """提取空间关系"""
        relationships = []
        
        try:
            for i, elem1 in enumerate(elements):
                for j, elem2 in enumerate(elements[i+1:], i+1):
                    # 计算两个元素的空间关系
                    relationship = self._calculate_spatial_relationship(elem1, elem2)
                    if relationship:
                        relationships.append(relationship)
            
            return relationships
            
        except Exception as e:
            logger.error(f"提取空间关系失败: {e}")
            return []
