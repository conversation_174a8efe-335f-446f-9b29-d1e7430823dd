"""
财务数据比对算法
"""
import re
from decimal import Decimal, InvalidOperation
from typing import List, Dict, Tuple, Optional, Any, Union
from loguru import logger

from src.offerDiff.models.ocr_result import OCRResult, TextType


class FinancialComparator:
    """财务数据比对器"""
    
    def __init__(self):
        self.amount_tolerance = 0.01  # 金额容差（1分）
        self.percentage_tolerance = 0.001  # 百分比容差（0.1%）
        self.currency_symbols = {"¥", "$", "€", "£", "￥"}
        self.number_patterns = {
            "currency": re.compile(r'[¥$€£￥]\s*[\d,]+\.?\d*'),
            "decimal": re.compile(r'\d+\.\d+'),
            "integer": re.compile(r'\d+'),
            "percentage": re.compile(r'\d+\.?\d*%')
        }
    
    def compare_financial_data(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """
        比较两个OCR结果的财务数据
        
        Args:
            ocr_result1: 第一个OCR结果
            ocr_result2: 第二个OCR结果
            
        Returns:
            财务数据比对结果
        """
        try:
            # 提取财务数据
            financial_data1 = self._extract_financial_data(ocr_result1)
            financial_data2 = self._extract_financial_data(ocr_result2)
            
            # 比较金额数据
            amount_comparison = self._compare_amounts(
                financial_data1["amounts"], financial_data2["amounts"]
            )
            
            # 比较数量数据
            quantity_comparison = self._compare_quantities(
                financial_data1["quantities"], financial_data2["quantities"]
            )
            
            # 比较百分比数据
            percentage_comparison = self._compare_percentages(
                financial_data1["percentages"], financial_data2["percentages"]
            )
            
            # 比较计算结果
            calculation_comparison = self._compare_calculations(
                financial_data1, financial_data2
            )
            
            # 比较财务术语
            term_comparison = self._compare_financial_terms(
                financial_data1["terms"], financial_data2["terms"]
            )
            
            # 计算整体财务一致性
            overall_consistency = self._calculate_overall_financial_consistency(
                amount_comparison, quantity_comparison, percentage_comparison, 
                calculation_comparison, term_comparison
            )
            
            result = {
                "overall_consistency": overall_consistency,
                "amount_comparison": amount_comparison,
                "quantity_comparison": quantity_comparison,
                "percentage_comparison": percentage_comparison,
                "calculation_comparison": calculation_comparison,
                "term_comparison": term_comparison,
                "financial_summary": {
                    "document1": self._summarize_financial_data(financial_data1),
                    "document2": self._summarize_financial_data(financial_data2)
                }
            }
            
            logger.info(f"财务数据比对完成，一致性: {overall_consistency:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"财务数据比对失败: {e}")
            return {
                "overall_consistency": 0.0,
                "error": str(e)
            }
    
    def _extract_financial_data(self, ocr_result: OCRResult) -> Dict:
        """提取财务数据"""
        try:
            financial_data = {
                "amounts": [],
                "quantities": [],
                "percentages": [],
                "terms": [],
                "dates": []
            }
            
            # 从文本块提取
            for text_block in ocr_result.text_blocks:
                self._extract_from_text(text_block.text, text_block.text_type, financial_data)
            
            # 从表格提取
            for table in ocr_result.tables:
                for cell in table.cells:
                    self._extract_from_text(cell.text, cell.text_type, financial_data)
            
            return financial_data
            
        except Exception as e:
            logger.error(f"提取财务数据失败: {e}")
            return {"amounts": [], "quantities": [], "percentages": [], "terms": [], "dates": []}
    
    def _extract_from_text(self, text: str, text_type: TextType, financial_data: Dict):
        """从文本中提取财务信息"""
        try:
            if not text or not text.strip():
                return
            
            # 提取金额
            if text_type == TextType.CURRENCY or self._contains_currency(text):
                amounts = self._extract_amounts(text)
                financial_data["amounts"].extend(amounts)
            
            # 提取数量
            elif text_type == TextType.NUMBER:
                quantities = self._extract_quantities(text)
                financial_data["quantities"].extend(quantities)
            
            # 提取百分比
            elif text_type == TextType.PERCENTAGE or "%" in text:
                percentages = self._extract_percentages(text)
                financial_data["percentages"].extend(percentages)
            
            # 提取财务术语
            elif text_type == TextType.FINANCIAL_TERM:
                terms = self._extract_financial_terms(text)
                financial_data["terms"].extend(terms)
            
            # 提取日期
            elif text_type == TextType.DATE:
                dates = self._extract_dates(text)
                financial_data["dates"].extend(dates)
            
        except Exception as e:
            logger.error(f"从文本提取财务信息失败: {e}")
    
    def _contains_currency(self, text: str) -> bool:
        """检查文本是否包含货币符号"""
        return any(symbol in text for symbol in self.currency_symbols)
    
    def _extract_amounts(self, text: str) -> List[Dict]:
        """提取金额"""
        amounts = []
        
        try:
            # 查找货币模式
            currency_matches = self.number_patterns["currency"].findall(text)
            
            for match in currency_matches:
                # 解析货币符号和数值
                currency_symbol = None
                for symbol in self.currency_symbols:
                    if symbol in match:
                        currency_symbol = symbol
                        break
                
                # 提取数值
                number_str = re.sub(r'[^\d.,]', '', match)
                number_str = number_str.replace(',', '')
                
                try:
                    value = Decimal(number_str)
                    amounts.append({
                        "original_text": match,
                        "value": float(value),
                        "currency": currency_symbol,
                        "formatted": match.strip()
                    })
                except (InvalidOperation, ValueError):
                    continue
            
            return amounts
            
        except Exception as e:
            logger.error(f"提取金额失败: {e}")
            return []
    
    def _extract_quantities(self, text: str) -> List[Dict]:
        """提取数量"""
        quantities = []
        
        try:
            # 查找数字模式
            decimal_matches = self.number_patterns["decimal"].findall(text)
            integer_matches = self.number_patterns["integer"].findall(text)
            
            all_numbers = decimal_matches + integer_matches
            
            for number_str in all_numbers:
                try:
                    value = Decimal(number_str)
                    quantities.append({
                        "original_text": number_str,
                        "value": float(value),
                        "unit": self._extract_unit(text, number_str)
                    })
                except (InvalidOperation, ValueError):
                    continue
            
            return quantities
            
        except Exception as e:
            logger.error(f"提取数量失败: {e}")
            return []
    
    def _extract_percentages(self, text: str) -> List[Dict]:
        """提取百分比"""
        percentages = []
        
        try:
            percentage_matches = self.number_patterns["percentage"].findall(text)
            
            for match in percentage_matches:
                number_str = match.replace('%', '')
                try:
                    value = Decimal(number_str)
                    percentages.append({
                        "original_text": match,
                        "value": float(value),
                        "decimal_value": float(value) / 100
                    })
                except (InvalidOperation, ValueError):
                    continue
            
            return percentages
            
        except Exception as e:
            logger.error(f"提取百分比失败: {e}")
            return []
    
    def _extract_financial_terms(self, text: str) -> List[Dict]:
        """提取财务术语"""
        terms = []
        
        try:
            # 常见财务术语
            financial_keywords = {
                "金额", "总价", "单价", "数量", "小计", "合计", "税率", "税额",
                "含税", "不含税", "人民币", "元", "万元", "千元", "美元", "欧元",
                "价格", "费用", "成本", "利润", "收入", "支出", "余额", "结算",
                "折扣", "优惠", "定金", "尾款", "预付款", "货款"
            }
            
            for keyword in financial_keywords:
                if keyword in text:
                    terms.append({
                        "term": keyword,
                        "context": text,
                        "category": self._categorize_financial_term(keyword)
                    })
            
            return terms
            
        except Exception as e:
            logger.error(f"提取财务术语失败: {e}")
            return []
    
    def _extract_dates(self, text: str) -> List[Dict]:
        """提取日期"""
        dates = []
        
        try:
            # 日期模式
            date_patterns = [
                r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',
                r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',
                r'\d{4}年\d{1,2}月\d{1,2}日'
            ]
            
            for pattern in date_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    dates.append({
                        "original_text": match,
                        "context": text
                    })
            
            return dates
            
        except Exception as e:
            logger.error(f"提取日期失败: {e}")
            return []
    
    def _extract_unit(self, text: str, number_str: str) -> Optional[str]:
        """提取数量单位"""
        try:
            # 查找数字后面的单位
            pattern = rf'{re.escape(number_str)}\s*([a-zA-Z\u4e00-\u9fff]+)'
            match = re.search(pattern, text)
            
            if match:
                return match.group(1)
            
            return None
            
        except Exception as e:
            logger.error(f"提取单位失败: {e}")
            return None
    
    def _categorize_financial_term(self, term: str) -> str:
        """分类财务术语"""
        categories = {
            "amount": ["金额", "总价", "单价", "价格", "费用", "成本"],
            "tax": ["税率", "税额", "含税", "不含税"],
            "currency": ["人民币", "元", "万元", "千元", "美元", "欧元"],
            "calculation": ["小计", "合计", "折扣", "优惠"],
            "payment": ["定金", "尾款", "预付款", "货款", "结算"]
        }
        
        for category, keywords in categories.items():
            if term in keywords:
                return category
        
        return "other"
