"""
Celery应用配置
"""
from celery import Celery
from loguru import logger

from config.settings import settings

# 创建Celery应用实例
celery_app = Celery(
    "pdf_comparison",
    broker=settings.redis_url,
    backend=settings.redis_url,
    include=[
        "src.tasks.comparison_task",
        "src.tasks.file_processing_task"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由
    task_routes={
        "src.tasks.comparison_task.*": {"queue": "comparison"},
        "src.tasks.file_processing_task.*": {"queue": "file_processing"},
    },
    
    # 任务配置
    task_always_eager=False,  # 是否立即执行任务（调试用）
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    
    # 工作进程配置
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # 结果过期时间
    result_expires=3600,  # 1小时
    
    # 任务重试配置
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
)

# 任务队列配置
celery_app.conf.task_default_queue = "default"
celery_app.conf.task_queues = {
    "default": {
        "exchange": "default",
        "routing_key": "default",
    },
    "comparison": {
        "exchange": "comparison",
        "routing_key": "comparison",
    },
    "file_processing": {
        "exchange": "file_processing", 
        "routing_key": "file_processing",
    },
}


@celery_app.task(bind=True)
def debug_task(self):
    """调试任务"""
    logger.info(f"Request: {self.request!r}")
    return "Debug task completed"


# 任务状态回调
@celery_app.task(bind=True)
def task_success_callback(self, retval, task_id, args, kwargs):
    """任务成功回调"""
    logger.info(f"任务 {task_id} 执行成功: {retval}")


@celery_app.task(bind=True)
def task_failure_callback(self, task_id, error, traceback, args, kwargs):
    """任务失败回调"""
    logger.error(f"任务 {task_id} 执行失败: {error}\n{traceback}")


# 启动时的初始化
@celery_app.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    """设置定期任务"""
    # 每30秒执行一次清理任务
    sender.add_periodic_task(30.0, cleanup_expired_tasks.s(), name="清理过期任务")


@celery_app.task
def cleanup_expired_tasks():
    """清理过期任务"""
    try:
        # TODO: 实现清理逻辑
        logger.info("执行定期清理任务")
        return "清理完成"
    except Exception as e:
        logger.error(f"清理任务失败: {e}")
        raise


if __name__ == "__main__":
    celery_app.start()
