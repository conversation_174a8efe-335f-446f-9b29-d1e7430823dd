"""
比对结果分析器
"""
import json
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from loguru import logger

from src.offerDiff.analysis.statistics import StatisticsCalculator


class ResultAnalyzer:
    """比对结果分析器"""
    
    def __init__(self):
        self.statistics = StatisticsCalculator()
        
        # 分析阈值
        self.thresholds = {
            "high_risk": 0.3,
            "medium_risk": 0.6,
            "low_risk": 0.8,
            "acceptable": 0.9
        }
        
        # 权重配置
        self.risk_weights = {
            "critical_differences": 0.5,
            "major_differences": 0.3,
            "minor_differences": 0.1,
            "overall_similarity": 0.1
        }
    
    def analyze_comparison_result(self, comparison_result: Dict) -> Dict:
        """
        分析比对结果
        
        Args:
            comparison_result: 比对结果
            
        Returns:
            分析结果
        """
        try:
            # 基础信息提取
            basic_analysis = self._extract_basic_analysis(comparison_result)
            
            # 风险评估
            risk_assessment = self._assess_risks(comparison_result)
            
            # 质量评估
            quality_assessment = self._assess_quality(comparison_result)
            
            # 一致性分析
            consistency_analysis = self._analyze_consistency(comparison_result)
            
            # 差异分析
            difference_analysis = self._analyze_differences(comparison_result)
            
            # 趋势分析
            trend_analysis = self._analyze_trends(comparison_result)
            
            # 建议生成
            recommendations = self._generate_recommendations(
                risk_assessment, quality_assessment, consistency_analysis, difference_analysis
            )
            
            # 决策支持
            decision_support = self._generate_decision_support(
                risk_assessment, recommendations
            )
            
            analysis_result = {
                "analysis_id": f"analysis_{int(datetime.utcnow().timestamp())}",
                "timestamp": datetime.utcnow().isoformat(),
                "basic_analysis": basic_analysis,
                "risk_assessment": risk_assessment,
                "quality_assessment": quality_assessment,
                "consistency_analysis": consistency_analysis,
                "difference_analysis": difference_analysis,
                "trend_analysis": trend_analysis,
                "recommendations": recommendations,
                "decision_support": decision_support,
                "summary": self._generate_analysis_summary(
                    basic_analysis, risk_assessment, quality_assessment
                )
            }
            
            logger.info(f"比对结果分析完成: 风险等级 {risk_assessment.get('risk_level', 'unknown')}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"比对结果分析失败: {e}")
            return {
                "analysis_id": f"analysis_error_{int(datetime.utcnow().timestamp())}",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "success": False
            }
    
    def _extract_basic_analysis(self, comparison_result: Dict) -> Dict:
        """提取基础分析信息"""
        try:
            basic_info = {
                "comparison_id": comparison_result.get("comparison_id", "unknown"),
                "overall_similarity": comparison_result.get("overall_similarity", 0.0),
                "similarity_level": comparison_result.get("similarity_level", "unknown"),
                "processing_time": comparison_result.get("processing_time", 0.0),
                "total_differences": 0,
                "document_info": {
                    "document1_id": comparison_result.get("metadata", {}).get("document1_id", "unknown"),
                    "document2_id": comparison_result.get("metadata", {}).get("document2_id", "unknown")
                }
            }
            
            # 统计差异数量
            differences = comparison_result.get("differences", {})
            basic_info["total_differences"] = (
                len(differences.get("critical_differences", [])) +
                len(differences.get("major_differences", [])) +
                len(differences.get("minor_differences", []))
            )
            
            # 提取各项分数
            basic_info["component_scores"] = {
                "text_similarity": comparison_result.get("text_comparison", {}).get("similarity", 0.0),
                "layout_similarity": comparison_result.get("layout_comparison", {}).get("overall_similarity", 0.0),
                "financial_consistency": comparison_result.get("financial_comparison", {}).get("overall_consistency", 0.0),
                "semantic_similarity": comparison_result.get("semantic_comparison", {}).get("overall_similarity", 0.0)
            }
            
            return basic_info
            
        except Exception as e:
            logger.error(f"提取基础分析信息失败: {e}")
            return {"error": str(e)}
    
    def _assess_risks(self, comparison_result: Dict) -> Dict:
        """评估风险"""
        try:
            differences = comparison_result.get("differences", {})
            
            # 统计各类差异
            critical_count = len(differences.get("critical_differences", []))
            major_count = len(differences.get("major_differences", []))
            minor_count = len(differences.get("minor_differences", []))
            
            # 计算风险分数
            risk_score = (
                critical_count * self.risk_weights["critical_differences"] +
                major_count * self.risk_weights["major_differences"] +
                minor_count * self.risk_weights["minor_differences"]
            )
            
            # 考虑整体相似度
            overall_similarity = comparison_result.get("overall_similarity", 0.0)
            similarity_risk = (1.0 - overall_similarity) * self.risk_weights["overall_similarity"]
            
            total_risk_score = risk_score + similarity_risk
            
            # 确定风险等级
            if total_risk_score >= self.thresholds["high_risk"]:
                risk_level = "high"
            elif total_risk_score >= self.thresholds["medium_risk"]:
                risk_level = "medium"
            elif total_risk_score >= self.thresholds["low_risk"]:
                risk_level = "low"
            else:
                risk_level = "minimal"
            
            # 识别主要风险因素
            risk_factors = self._identify_risk_factors(differences, comparison_result)
            
            # 风险影响评估
            impact_assessment = self._assess_risk_impact(differences, comparison_result)
            
            return {
                "risk_level": risk_level,
                "risk_score": total_risk_score,
                "risk_factors": risk_factors,
                "impact_assessment": impact_assessment,
                "difference_counts": {
                    "critical": critical_count,
                    "major": major_count,
                    "minor": minor_count
                },
                "risk_breakdown": {
                    "critical_risk": critical_count * self.risk_weights["critical_differences"],
                    "major_risk": major_count * self.risk_weights["major_differences"],
                    "minor_risk": minor_count * self.risk_weights["minor_differences"],
                    "similarity_risk": similarity_risk
                }
            }
            
        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return {"risk_level": "unknown", "error": str(e)}
    
    def _identify_risk_factors(self, differences: Dict, comparison_result: Dict) -> List[Dict]:
        """识别风险因素"""
        risk_factors = []
        
        try:
            # 分析关键差异
            critical_differences = differences.get("critical_differences", [])
            
            for diff in critical_differences:
                risk_factor = {
                    "type": diff.get("type", "unknown"),
                    "category": diff.get("category", "unknown"),
                    "description": diff.get("description", ""),
                    "severity": "high",
                    "impact": self._assess_difference_impact(diff)
                }
                risk_factors.append(risk_factor)
            
            # 分析财务风险
            financial_comparison = comparison_result.get("financial_comparison", {})
            if financial_comparison.get("overall_consistency", 1.0) < 0.8:
                risk_factors.append({
                    "type": "financial_inconsistency",
                    "category": "financial",
                    "description": "财务数据存在不一致",
                    "severity": "high",
                    "impact": "可能影响合同执行和财务结算"
                })
            
            # 分析布局风险
            layout_comparison = comparison_result.get("layout_comparison", {})
            if layout_comparison.get("overall_similarity", 1.0) < 0.6:
                risk_factors.append({
                    "type": "layout_inconsistency",
                    "category": "layout",
                    "description": "文档布局存在显著差异",
                    "severity": "medium",
                    "impact": "可能影响文档的完整性和可读性"
                })
            
            return risk_factors
            
        except Exception as e:
            logger.error(f"识别风险因素失败: {e}")
            return []
    
    def _assess_difference_impact(self, difference: Dict) -> str:
        """评估差异影响"""
        try:
            diff_type = difference.get("type", "")
            category = difference.get("category", "")
            
            if category == "financial":
                return "可能影响合同金额和财务结算"
            elif category == "temporal":
                return "可能影响时间安排和交付计划"
            elif category == "content" and "关键" in difference.get("description", ""):
                return "可能影响合同核心条款"
            elif category == "layout":
                return "可能影响文档完整性"
            else:
                return "影响程度需要进一步评估"
                
        except Exception as e:
            logger.error(f"评估差异影响失败: {e}")
            return "未知影响"
    
    def _assess_risk_impact(self, differences: Dict, comparison_result: Dict) -> Dict:
        """评估风险影响"""
        try:
            impact_areas = {
                "financial": 0,
                "legal": 0,
                "operational": 0,
                "compliance": 0
            }
            
            # 分析各类差异的影响
            all_differences = (
                differences.get("critical_differences", []) +
                differences.get("major_differences", []) +
                differences.get("minor_differences", [])
            )
            
            for diff in all_differences:
                category = diff.get("category", "")
                severity = diff.get("severity", "minor")
                
                weight = {"critical": 3, "major": 2, "minor": 1}.get(severity, 1)
                
                if category == "financial":
                    impact_areas["financial"] += weight
                    impact_areas["legal"] += weight * 0.5
                elif category == "content":
                    impact_areas["legal"] += weight
                    impact_areas["compliance"] += weight * 0.7
                elif category == "temporal":
                    impact_areas["operational"] += weight
                elif category == "layout":
                    impact_areas["compliance"] += weight * 0.3
            
            # 标准化影响分数
            max_impact = max(impact_areas.values()) if impact_areas.values() else 1
            normalized_impact = {
                area: score / max_impact for area, score in impact_areas.items()
            }
            
            return {
                "impact_scores": normalized_impact,
                "primary_impact_area": max(normalized_impact, key=normalized_impact.get),
                "overall_impact_level": self._classify_impact_level(max(normalized_impact.values()))
            }
            
        except Exception as e:
            logger.error(f"评估风险影响失败: {e}")
            return {"overall_impact_level": "unknown"}
    
    def _classify_impact_level(self, impact_score: float) -> str:
        """分类影响等级"""
        if impact_score >= 0.8:
            return "high"
        elif impact_score >= 0.5:
            return "medium"
        elif impact_score >= 0.2:
            return "low"
        else:
            return "minimal"
    
    def _assess_quality(self, comparison_result: Dict) -> Dict:
        """评估质量"""
        try:
            # 提取置信度信息
            text_comparison = comparison_result.get("text_comparison", {})
            layout_comparison = comparison_result.get("layout_comparison", {})
            financial_comparison = comparison_result.get("financial_comparison", {})
            semantic_comparison = comparison_result.get("semantic_comparison", {})
            
            # 计算各项质量指标
            quality_metrics = {
                "text_quality": self._assess_text_quality(text_comparison),
                "layout_quality": self._assess_layout_quality(layout_comparison),
                "financial_quality": self._assess_financial_quality(financial_comparison),
                "semantic_quality": self._assess_semantic_quality(semantic_comparison)
            }
            
            # 计算整体质量分数
            overall_quality = sum(quality_metrics.values()) / len(quality_metrics)
            
            # 质量等级分类
            if overall_quality >= 0.9:
                quality_level = "excellent"
            elif overall_quality >= 0.8:
                quality_level = "good"
            elif overall_quality >= 0.6:
                quality_level = "fair"
            else:
                quality_level = "poor"
            
            return {
                "overall_quality": overall_quality,
                "quality_level": quality_level,
                "quality_metrics": quality_metrics,
                "quality_issues": self._identify_quality_issues(quality_metrics)
            }
            
        except Exception as e:
            logger.error(f"质量评估失败: {e}")
            return {"overall_quality": 0.0, "quality_level": "unknown"}
    
    def _assess_text_quality(self, text_comparison: Dict) -> float:
        """评估文本质量"""
        try:
            if not text_comparison.get("success", True):
                return 0.0
            
            similarity = text_comparison.get("similarity", 0.0)
            
            # 检查是否有详细信息
            details = text_comparison.get("details", {})
            if details:
                return min(1.0, similarity + 0.1)  # 有详细信息加分
            
            return similarity
            
        except Exception as e:
            logger.error(f"评估文本质量失败: {e}")
            return 0.0
    
    def _assess_layout_quality(self, layout_comparison: Dict) -> float:
        """评估布局质量"""
        try:
            if not layout_comparison.get("success", True):
                return 0.0
            
            similarity = layout_comparison.get("overall_similarity", 0.0)
            
            # 检查是否有详细的布局分析
            text_layout = layout_comparison.get("text_layout", {})
            table_layout = layout_comparison.get("table_layout", {})
            
            if text_layout and table_layout:
                return min(1.0, similarity + 0.05)  # 有详细分析加分
            
            return similarity
            
        except Exception as e:
            logger.error(f"评估布局质量失败: {e}")
            return 0.0
    
    def _assess_financial_quality(self, financial_comparison: Dict) -> float:
        """评估财务质量"""
        try:
            if not financial_comparison.get("success", True):
                return 0.0
            
            consistency = financial_comparison.get("overall_consistency", 0.0)
            
            # 检查是否有详细的财务分析
            amount_comparison = financial_comparison.get("amount_comparison", {})
            if amount_comparison and amount_comparison.get("currency_consistent", False):
                return min(1.0, consistency + 0.1)  # 货币一致性加分
            
            return consistency
            
        except Exception as e:
            logger.error(f"评估财务质量失败: {e}")
            return 0.0
    
    def _assess_semantic_quality(self, semantic_comparison: Dict) -> float:
        """评估语义质量"""
        try:
            if not semantic_comparison.get("success", True):
                return 0.0
            
            similarity = semantic_comparison.get("overall_similarity", 0.0)
            
            # 检查LLM分析质量
            llm_analysis = semantic_comparison.get("llm_analysis", {})
            if llm_analysis.get("success", False):
                confidence = llm_analysis.get("confidence", 0.0)
                return min(1.0, (similarity + confidence) / 2)
            
            return similarity
            
        except Exception as e:
            logger.error(f"评估语义质量失败: {e}")
            return 0.0
    
    def _identify_quality_issues(self, quality_metrics: Dict) -> List[str]:
        """识别质量问题"""
        issues = []
        
        try:
            for metric, score in quality_metrics.items():
                if score < 0.6:
                    issues.append(f"{metric}质量较低 (分数: {score:.2f})")
                elif score < 0.8:
                    issues.append(f"{metric}质量一般 (分数: {score:.2f})")
            
            return issues
            
        except Exception as e:
            logger.error(f"识别质量问题失败: {e}")
            return []
