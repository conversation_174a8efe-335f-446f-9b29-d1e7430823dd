"""
报告生成器
"""
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path
from loguru import logger

try:
    from jinja2 import Environment, FileSystemLoader, Template
    JINJA2_AVAILABLE = True
except ImportError:
    logger.warning("Jinja2未安装，将使用基础模板功能")
    JINJA2_AVAILABLE = False


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.template_dir = Path("templates/reports")
        self.output_dir = Path("outputs/reports")
        
        # 确保目录存在
        self.template_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化模板环境
        if JINJA2_AVAILABLE:
            self.env = Environment(loader=FileSystemLoader(str(self.template_dir)))
        else:
            self.env = None
        
        # 创建默认模板
        self._create_default_templates()
    
    def generate_comparison_report(self, comparison_result: Dict, analysis_result: Dict, 
                                 report_format: str = "html") -> Dict:
        """
        生成比对报告
        
        Args:
            comparison_result: 比对结果
            analysis_result: 分析结果
            report_format: 报告格式 ("html", "json", "text")
            
        Returns:
            报告生成结果
        """
        try:
            # 准备报告数据
            report_data = self._prepare_report_data(comparison_result, analysis_result)
            
            # 根据格式生成报告
            if report_format.lower() == "html":
                report_content = self._generate_html_report(report_data)
                file_extension = ".html"
            elif report_format.lower() == "json":
                report_content = self._generate_json_report(report_data)
                file_extension = ".json"
            elif report_format.lower() == "text":
                report_content = self._generate_text_report(report_data)
                file_extension = ".txt"
            else:
                raise ValueError(f"不支持的报告格式: {report_format}")
            
            # 生成文件名
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            comparison_id = comparison_result.get("comparison_id", "unknown")
            filename = f"comparison_report_{comparison_id}_{timestamp}{file_extension}"
            
            # 保存报告
            report_path = self.output_dir / filename
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            result = {
                "success": True,
                "report_path": str(report_path),
                "filename": filename,
                "format": report_format,
                "size": len(report_content),
                "generated_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"比对报告生成完成: {filename}")
            return result
            
        except Exception as e:
            logger.error(f"生成比对报告失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "generated_at": datetime.utcnow().isoformat()
            }
    
    def _prepare_report_data(self, comparison_result: Dict, analysis_result: Dict) -> Dict:
        """准备报告数据"""
        try:
            # 基础信息
            basic_info = {
                "comparison_id": comparison_result.get("comparison_id", "unknown"),
                "timestamp": comparison_result.get("timestamp", datetime.utcnow().isoformat()),
                "processing_time": comparison_result.get("processing_time", 0.0),
                "document1_id": comparison_result.get("metadata", {}).get("document1_id", "unknown"),
                "document2_id": comparison_result.get("metadata", {}).get("document2_id", "unknown")
            }
            
            # 比对结果摘要
            comparison_summary = {
                "overall_similarity": comparison_result.get("overall_similarity", 0.0),
                "similarity_level": comparison_result.get("similarity_level", "unknown"),
                "component_scores": {
                    "text_similarity": comparison_result.get("text_comparison", {}).get("similarity", 0.0),
                    "layout_similarity": comparison_result.get("layout_comparison", {}).get("overall_similarity", 0.0),
                    "financial_consistency": comparison_result.get("financial_comparison", {}).get("overall_consistency", 0.0),
                    "semantic_similarity": comparison_result.get("semantic_comparison", {}).get("overall_similarity", 0.0)
                }
            }
            
            # 差异分析
            differences = comparison_result.get("differences", {})
            difference_summary = {
                "critical_count": len(differences.get("critical_differences", [])),
                "major_count": len(differences.get("major_differences", [])),
                "minor_count": len(differences.get("minor_differences", [])),
                "total_count": len(differences.get("critical_differences", [])) + 
                              len(differences.get("major_differences", [])) + 
                              len(differences.get("minor_differences", []))
            }
            
            # 风险评估
            risk_assessment = analysis_result.get("risk_assessment", {})
            
            # 质量评估
            quality_assessment = analysis_result.get("quality_assessment", {})
            
            # 建议
            recommendations = analysis_result.get("recommendations", [])
            
            # 决策支持
            decision_support = analysis_result.get("decision_support", {})
            
            report_data = {
                "basic_info": basic_info,
                "comparison_summary": comparison_summary,
                "difference_summary": difference_summary,
                "differences": differences,
                "risk_assessment": risk_assessment,
                "quality_assessment": quality_assessment,
                "recommendations": recommendations,
                "decision_support": decision_support,
                "generated_at": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S"),
                "report_version": "1.0"
            }
            
            return report_data
            
        except Exception as e:
            logger.error(f"准备报告数据失败: {e}")
            return {}
    
    def _generate_html_report(self, report_data: Dict) -> str:
        """生成HTML报告"""
        try:
            if JINJA2_AVAILABLE and self.env:
                # 使用Jinja2模板
                template = self.env.get_template("comparison_report.html")
                return template.render(**report_data)
            else:
                # 使用基础HTML模板
                return self._generate_basic_html_report(report_data)
                
        except Exception as e:
            logger.error(f"生成HTML报告失败: {e}")
            return self._generate_basic_html_report(report_data)
    
    def _generate_basic_html_report(self, report_data: Dict) -> str:
        """生成基础HTML报告"""
        try:
            basic_info = report_data.get("basic_info", {})
            comparison_summary = report_data.get("comparison_summary", {})
            difference_summary = report_data.get("difference_summary", {})
            risk_assessment = report_data.get("risk_assessment", {})
            
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档比对报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .score {{ font-size: 24px; font-weight: bold; color: #2c3e50; }}
        .risk-high {{ color: #e74c3c; }}
        .risk-medium {{ color: #f39c12; }}
        .risk-low {{ color: #27ae60; }}
        .difference {{ margin: 10px 0; padding: 10px; background-color: #f9f9f9; border-left: 4px solid #3498db; }}
        .critical {{ border-left-color: #e74c3c; }}
        .major {{ border-left-color: #f39c12; }}
        .minor {{ border-left-color: #27ae60; }}
        table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>文档比对报告</h1>
        <p><strong>比对ID:</strong> {basic_info.get('comparison_id', 'N/A')}</p>
        <p><strong>生成时间:</strong> {report_data.get('generated_at', 'N/A')}</p>
        <p><strong>处理时间:</strong> {basic_info.get('processing_time', 0):.2f} 秒</p>
    </div>
    
    <div class="section">
        <h2>比对结果摘要</h2>
        <p class="score">整体相似度: {comparison_summary.get('overall_similarity', 0):.1%}</p>
        <p><strong>相似度等级:</strong> {comparison_summary.get('similarity_level', 'N/A')}</p>
        
        <h3>各项分数</h3>
        <table>
            <tr><th>项目</th><th>分数</th></tr>
            <tr><td>文本相似度</td><td>{comparison_summary.get('component_scores', {}).get('text_similarity', 0):.1%}</td></tr>
            <tr><td>布局相似度</td><td>{comparison_summary.get('component_scores', {}).get('layout_similarity', 0):.1%}</td></tr>
            <tr><td>财务一致性</td><td>{comparison_summary.get('component_scores', {}).get('financial_consistency', 0):.1%}</td></tr>
            <tr><td>语义相似度</td><td>{comparison_summary.get('component_scores', {}).get('semantic_similarity', 0):.1%}</td></tr>
        </table>
    </div>
    
    <div class="section">
        <h2>差异统计</h2>
        <p><strong>总差异数:</strong> {difference_summary.get('total_count', 0)}</p>
        <ul>
            <li><span class="risk-high">关键差异:</span> {difference_summary.get('critical_count', 0)}</li>
            <li><span class="risk-medium">重要差异:</span> {difference_summary.get('major_count', 0)}</li>
            <li><span class="risk-low">轻微差异:</span> {difference_summary.get('minor_count', 0)}</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>风险评估</h2>
        <p><strong>风险等级:</strong> <span class="risk-{risk_assessment.get('risk_level', 'unknown')}">{risk_assessment.get('risk_level', 'N/A').upper()}</span></p>
        <p><strong>风险分数:</strong> {risk_assessment.get('risk_score', 0):.3f}</p>
    </div>
    
    <div class="section">
        <h2>详细差异</h2>
        {self._format_differences_html(report_data.get('differences', {}))}
    </div>
    
    <div class="section">
        <h2>建议</h2>
        {self._format_recommendations_html(report_data.get('recommendations', []))}
    </div>
    
    <footer style="margin-top: 40px; text-align: center; color: #666;">
        <p>报告由合同比对系统自动生成 - 版本 {report_data.get('report_version', '1.0')}</p>
    </footer>
</body>
</html>
"""
            return html_content
            
        except Exception as e:
            logger.error(f"生成基础HTML报告失败: {e}")
            return f"<html><body><h1>报告生成失败</h1><p>错误: {str(e)}</p></body></html>"
    
    def _format_differences_html(self, differences: Dict) -> str:
        """格式化差异为HTML"""
        try:
            html_parts = []
            
            for severity in ["critical_differences", "major_differences", "minor_differences"]:
                diff_list = differences.get(severity, [])
                if diff_list:
                    severity_name = {"critical_differences": "关键", "major_differences": "重要", "minor_differences": "轻微"}[severity]
                    severity_class = {"critical_differences": "critical", "major_differences": "major", "minor_differences": "minor"}[severity]
                    
                    html_parts.append(f"<h3>{severity_name}差异</h3>")
                    
                    for diff in diff_list[:10]:  # 限制显示数量
                        description = diff.get("description", "无描述")
                        html_parts.append(f'<div class="difference {severity_class}">{description}</div>')
                    
                    if len(diff_list) > 10:
                        html_parts.append(f"<p>... 还有 {len(diff_list) - 10} 个{severity_name}差异</p>")
            
            return "\n".join(html_parts) if html_parts else "<p>未发现差异</p>"
            
        except Exception as e:
            logger.error(f"格式化差异HTML失败: {e}")
            return "<p>差异信息格式化失败</p>"
    
    def _format_recommendations_html(self, recommendations: List) -> str:
        """格式化建议为HTML"""
        try:
            if not recommendations:
                return "<p>暂无建议</p>"
            
            html_parts = ["<ul>"]
            for rec in recommendations:
                if isinstance(rec, dict):
                    text = rec.get("text", str(rec))
                else:
                    text = str(rec)
                html_parts.append(f"<li>{text}</li>")
            html_parts.append("</ul>")
            
            return "\n".join(html_parts)
            
        except Exception as e:
            logger.error(f"格式化建议HTML失败: {e}")
            return "<p>建议信息格式化失败</p>"
    
    def _generate_json_report(self, report_data: Dict) -> str:
        """生成JSON报告"""
        try:
            return json.dumps(report_data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"生成JSON报告失败: {e}")
            return json.dumps({"error": str(e)}, ensure_ascii=False, indent=2)
    
    def _generate_text_report(self, report_data: Dict) -> str:
        """生成文本报告"""
        try:
            basic_info = report_data.get("basic_info", {})
            comparison_summary = report_data.get("comparison_summary", {})
            difference_summary = report_data.get("difference_summary", {})
            risk_assessment = report_data.get("risk_assessment", {})
            
            text_parts = [
                "=" * 60,
                "文档比对报告",
                "=" * 60,
                "",
                f"比对ID: {basic_info.get('comparison_id', 'N/A')}",
                f"生成时间: {report_data.get('generated_at', 'N/A')}",
                f"处理时间: {basic_info.get('processing_time', 0):.2f} 秒",
                "",
                "比对结果摘要",
                "-" * 30,
                f"整体相似度: {comparison_summary.get('overall_similarity', 0):.1%}",
                f"相似度等级: {comparison_summary.get('similarity_level', 'N/A')}",
                "",
                "各项分数:",
                f"  文本相似度: {comparison_summary.get('component_scores', {}).get('text_similarity', 0):.1%}",
                f"  布局相似度: {comparison_summary.get('component_scores', {}).get('layout_similarity', 0):.1%}",
                f"  财务一致性: {comparison_summary.get('component_scores', {}).get('financial_consistency', 0):.1%}",
                f"  语义相似度: {comparison_summary.get('component_scores', {}).get('semantic_similarity', 0):.1%}",
                "",
                "差异统计",
                "-" * 30,
                f"总差异数: {difference_summary.get('total_count', 0)}",
                f"  关键差异: {difference_summary.get('critical_count', 0)}",
                f"  重要差异: {difference_summary.get('major_count', 0)}",
                f"  轻微差异: {difference_summary.get('minor_count', 0)}",
                "",
                "风险评估",
                "-" * 30,
                f"风险等级: {risk_assessment.get('risk_level', 'N/A').upper()}",
                f"风险分数: {risk_assessment.get('risk_score', 0):.3f}",
                "",
                "建议",
                "-" * 30
            ]
            
            # 添加建议
            recommendations = report_data.get("recommendations", [])
            if recommendations:
                for i, rec in enumerate(recommendations, 1):
                    if isinstance(rec, dict):
                        text = rec.get("text", str(rec))
                    else:
                        text = str(rec)
                    text_parts.append(f"{i}. {text}")
            else:
                text_parts.append("暂无建议")
            
            text_parts.extend([
                "",
                "=" * 60,
                f"报告由合同比对系统自动生成 - 版本 {report_data.get('report_version', '1.0')}"
            ])
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.error(f"生成文本报告失败: {e}")
            return f"报告生成失败: {str(e)}"
    
    def _create_default_templates(self):
        """创建默认模板"""
        try:
            if not JINJA2_AVAILABLE:
                return
            
            # HTML模板
            html_template_path = self.template_dir / "comparison_report.html"
            if not html_template_path.exists():
                html_template_content = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档比对报告</title>
    <style>
        /* CSS样式 */
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        /* 更多样式... */
    </style>
</head>
<body>
    <div class="header">
        <h1>文档比对报告</h1>
        <p><strong>比对ID:</strong> {{ basic_info.comparison_id }}</p>
        <p><strong>生成时间:</strong> {{ generated_at }}</p>
    </div>
    
    <div class="section">
        <h2>比对结果摘要</h2>
        <p class="score">整体相似度: {{ "%.1f%%" | format(comparison_summary.overall_similarity * 100) }}</p>
        <!-- 更多内容... -->
    </div>
    
    <!-- 更多部分... -->
</body>
</html>
"""
                with open(html_template_path, 'w', encoding='utf-8') as f:
                    f.write(html_template_content)
                
                logger.info("创建默认HTML模板")
                
        except Exception as e:
            logger.error(f"创建默认模板失败: {e}")
    
    def get_available_formats(self) -> List[str]:
        """获取可用的报告格式"""
        return ["html", "json", "text"]
    
    def get_report_list(self) -> List[Dict]:
        """获取已生成的报告列表"""
        try:
            reports = []
            
            for file_path in self.output_dir.glob("comparison_report_*"):
                if file_path.is_file():
                    stat = file_path.stat()
                    reports.append({
                        "filename": file_path.name,
                        "path": str(file_path),
                        "size": stat.st_size,
                        "created_at": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        "modified_at": datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            # 按创建时间排序
            reports.sort(key=lambda x: x["created_at"], reverse=True)
            
            return reports
            
        except Exception as e:
            logger.error(f"获取报告列表失败: {e}")
            return []
