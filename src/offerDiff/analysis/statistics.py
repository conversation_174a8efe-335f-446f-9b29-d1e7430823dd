"""
统计分析模块
"""
import math
from typing import Dict, List, Optional, Any, Tuple
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from loguru import logger

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    logger.warning("NumPy未安装，将使用基础统计功能")
    NUMPY_AVAILABLE = False


class StatisticsCalculator:
    """统计计算器"""
    
    def __init__(self):
        self.comparison_history = []
        self.performance_metrics = []
    
    def calculate_comparison_statistics(self, comparison_results: List[Dict]) -> Dict:
        """
        计算比对统计信息
        
        Args:
            comparison_results: 比对结果列表
            
        Returns:
            统计信息
        """
        try:
            if not comparison_results:
                return {"error": "没有比对结果数据"}
            
            # 基础统计
            basic_stats = self._calculate_basic_statistics(comparison_results)
            
            # 相似度分布
            similarity_distribution = self._analyze_similarity_distribution(comparison_results)
            
            # 差异统计
            difference_statistics = self._analyze_difference_statistics(comparison_results)
            
            # 性能统计
            performance_statistics = self._analyze_performance_statistics(comparison_results)
            
            # 趋势分析
            trend_analysis = self._analyze_trends(comparison_results)
            
            # 质量指标
            quality_metrics = self._calculate_quality_metrics(comparison_results)
            
            statistics = {
                "summary": {
                    "total_comparisons": len(comparison_results),
                    "analysis_period": self._get_analysis_period(comparison_results),
                    "generated_at": datetime.utcnow().isoformat()
                },
                "basic_statistics": basic_stats,
                "similarity_distribution": similarity_distribution,
                "difference_statistics": difference_statistics,
                "performance_statistics": performance_statistics,
                "trend_analysis": trend_analysis,
                "quality_metrics": quality_metrics
            }
            
            logger.info(f"统计分析完成: {len(comparison_results)}个比对结果")
            return statistics
            
        except Exception as e:
            logger.error(f"计算比对统计信息失败: {e}")
            return {"error": str(e)}
    
    def _calculate_basic_statistics(self, comparison_results: List[Dict]) -> Dict:
        """计算基础统计信息"""
        try:
            similarities = []
            processing_times = []
            
            for result in comparison_results:
                similarity = result.get("overall_similarity", 0.0)
                similarities.append(similarity)
                
                processing_time = result.get("processing_time", 0.0)
                processing_times.append(processing_time)
            
            # 相似度统计
            similarity_stats = self._calculate_descriptive_stats(similarities)
            
            # 处理时间统计
            time_stats = self._calculate_descriptive_stats(processing_times)
            
            # 成功率统计
            successful_comparisons = sum(1 for r in comparison_results if not r.get("error"))
            success_rate = successful_comparisons / len(comparison_results)
            
            return {
                "similarity_statistics": similarity_stats,
                "processing_time_statistics": time_stats,
                "success_rate": success_rate,
                "successful_comparisons": successful_comparisons,
                "failed_comparisons": len(comparison_results) - successful_comparisons
            }
            
        except Exception as e:
            logger.error(f"计算基础统计信息失败: {e}")
            return {}
    
    def _calculate_descriptive_stats(self, values: List[float]) -> Dict:
        """计算描述性统计"""
        try:
            if not values:
                return {"count": 0}
            
            if NUMPY_AVAILABLE:
                # 使用NumPy计算
                arr = np.array(values)
                stats = {
                    "count": len(values),
                    "mean": float(np.mean(arr)),
                    "median": float(np.median(arr)),
                    "std": float(np.std(arr)),
                    "min": float(np.min(arr)),
                    "max": float(np.max(arr)),
                    "q25": float(np.percentile(arr, 25)),
                    "q75": float(np.percentile(arr, 75))
                }
            else:
                # 使用基础Python计算
                sorted_values = sorted(values)
                n = len(values)
                
                stats = {
                    "count": n,
                    "mean": sum(values) / n,
                    "median": sorted_values[n // 2] if n % 2 == 1 else (sorted_values[n // 2 - 1] + sorted_values[n // 2]) / 2,
                    "min": min(values),
                    "max": max(values)
                }
                
                # 计算标准差
                mean_val = stats["mean"]
                variance = sum((x - mean_val) ** 2 for x in values) / n
                stats["std"] = math.sqrt(variance)
                
                # 计算四分位数
                q1_idx = n // 4
                q3_idx = 3 * n // 4
                stats["q25"] = sorted_values[q1_idx]
                stats["q75"] = sorted_values[q3_idx]
            
            return stats
            
        except Exception as e:
            logger.error(f"计算描述性统计失败: {e}")
            return {"count": 0, "error": str(e)}
    
    def _analyze_similarity_distribution(self, comparison_results: List[Dict]) -> Dict:
        """分析相似度分布"""
        try:
            similarities = [r.get("overall_similarity", 0.0) for r in comparison_results]
            
            # 分布区间统计
            bins = {
                "very_high": 0,  # 0.9-1.0
                "high": 0,       # 0.8-0.9
                "medium": 0,     # 0.6-0.8
                "low": 0,        # 0.4-0.6
                "very_low": 0    # 0.0-0.4
            }
            
            for sim in similarities:
                if sim >= 0.9:
                    bins["very_high"] += 1
                elif sim >= 0.8:
                    bins["high"] += 1
                elif sim >= 0.6:
                    bins["medium"] += 1
                elif sim >= 0.4:
                    bins["low"] += 1
                else:
                    bins["very_low"] += 1
            
            # 转换为百分比
            total = len(similarities)
            distribution = {k: (v / total * 100) if total > 0 else 0 for k, v in bins.items()}
            
            # 组件相似度分析
            component_analysis = self._analyze_component_similarities(comparison_results)
            
            return {
                "distribution": distribution,
                "bin_counts": bins,
                "component_analysis": component_analysis
            }
            
        except Exception as e:
            logger.error(f"分析相似度分布失败: {e}")
            return {}
    
    def _analyze_component_similarities(self, comparison_results: List[Dict]) -> Dict:
        """分析组件相似度"""
        try:
            components = {
                "text_similarity": [],
                "layout_similarity": [],
                "financial_consistency": [],
                "semantic_similarity": []
            }
            
            for result in comparison_results:
                text_comp = result.get("text_comparison", {})
                layout_comp = result.get("layout_comparison", {})
                financial_comp = result.get("financial_comparison", {})
                semantic_comp = result.get("semantic_comparison", {})
                
                components["text_similarity"].append(text_comp.get("similarity", 0.0))
                components["layout_similarity"].append(layout_comp.get("overall_similarity", 0.0))
                components["financial_consistency"].append(financial_comp.get("overall_consistency", 0.0))
                components["semantic_similarity"].append(semantic_comp.get("overall_similarity", 0.0))
            
            # 计算各组件统计
            component_stats = {}
            for component, values in components.items():
                component_stats[component] = self._calculate_descriptive_stats(values)
            
            return component_stats
            
        except Exception as e:
            logger.error(f"分析组件相似度失败: {e}")
            return {}
    
    def _analyze_difference_statistics(self, comparison_results: List[Dict]) -> Dict:
        """分析差异统计"""
        try:
            difference_counts = {
                "critical": [],
                "major": [],
                "minor": [],
                "total": []
            }
            
            difference_types = Counter()
            difference_categories = Counter()
            
            for result in comparison_results:
                differences = result.get("differences", {})
                
                critical_count = len(differences.get("critical_differences", []))
                major_count = len(differences.get("major_differences", []))
                minor_count = len(differences.get("minor_differences", []))
                total_count = critical_count + major_count + minor_count
                
                difference_counts["critical"].append(critical_count)
                difference_counts["major"].append(major_count)
                difference_counts["minor"].append(minor_count)
                difference_counts["total"].append(total_count)
                
                # 统计差异类型和类别
                all_differences = (
                    differences.get("critical_differences", []) +
                    differences.get("major_differences", []) +
                    differences.get("minor_differences", [])
                )
                
                for diff in all_differences:
                    diff_type = diff.get("type", "unknown")
                    diff_category = diff.get("category", "unknown")
                    difference_types[diff_type] += 1
                    difference_categories[diff_category] += 1
            
            # 计算差异统计
            difference_stats = {}
            for severity, counts in difference_counts.items():
                difference_stats[severity] = self._calculate_descriptive_stats(counts)
            
            return {
                "difference_counts": difference_stats,
                "most_common_types": dict(difference_types.most_common(10)),
                "most_common_categories": dict(difference_categories.most_common(10)),
                "type_distribution": dict(difference_types),
                "category_distribution": dict(difference_categories)
            }
            
        except Exception as e:
            logger.error(f"分析差异统计失败: {e}")
            return {}
    
    def _analyze_performance_statistics(self, comparison_results: List[Dict]) -> Dict:
        """分析性能统计"""
        try:
            processing_times = [r.get("processing_time", 0.0) for r in comparison_results]
            
            # 处理时间统计
            time_stats = self._calculate_descriptive_stats(processing_times)
            
            # 性能分级
            performance_bins = {
                "fast": 0,      # < 5秒
                "normal": 0,    # 5-15秒
                "slow": 0,      # 15-30秒
                "very_slow": 0  # > 30秒
            }
            
            for time_val in processing_times:
                if time_val < 5:
                    performance_bins["fast"] += 1
                elif time_val < 15:
                    performance_bins["normal"] += 1
                elif time_val < 30:
                    performance_bins["slow"] += 1
                else:
                    performance_bins["very_slow"] += 1
            
            # 计算吞吐量
            total_time = sum(processing_times)
            throughput = len(comparison_results) / total_time if total_time > 0 else 0
            
            return {
                "processing_time_stats": time_stats,
                "performance_distribution": performance_bins,
                "throughput": throughput,  # 比对/秒
                "total_processing_time": total_time
            }
            
        except Exception as e:
            logger.error(f"分析性能统计失败: {e}")
            return {}
    
    def _analyze_trends(self, comparison_results: List[Dict]) -> Dict:
        """分析趋势"""
        try:
            # 按时间排序
            sorted_results = sorted(
                comparison_results,
                key=lambda x: x.get("timestamp", ""),
                reverse=False
            )
            
            if len(sorted_results) < 2:
                return {"trend": "insufficient_data"}
            
            # 相似度趋势
            similarities = [r.get("overall_similarity", 0.0) for r in sorted_results]
            similarity_trend = self._calculate_trend(similarities)
            
            # 处理时间趋势
            processing_times = [r.get("processing_time", 0.0) for r in sorted_results]
            performance_trend = self._calculate_trend(processing_times)
            
            # 差异数量趋势
            difference_counts = []
            for result in sorted_results:
                differences = result.get("differences", {})
                total_diff = (
                    len(differences.get("critical_differences", [])) +
                    len(differences.get("major_differences", [])) +
                    len(differences.get("minor_differences", []))
                )
                difference_counts.append(total_diff)
            
            difference_trend = self._calculate_trend(difference_counts)
            
            return {
                "similarity_trend": similarity_trend,
                "performance_trend": performance_trend,
                "difference_trend": difference_trend,
                "data_points": len(sorted_results)
            }
            
        except Exception as e:
            logger.error(f"分析趋势失败: {e}")
            return {"trend": "analysis_failed"}
    
    def _calculate_trend(self, values: List[float]) -> Dict:
        """计算趋势"""
        try:
            if len(values) < 2:
                return {"direction": "unknown", "strength": 0.0}
            
            # 简单线性趋势计算
            n = len(values)
            x = list(range(n))
            
            # 计算斜率
            x_mean = sum(x) / n
            y_mean = sum(values) / n
            
            numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
            denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
            
            if denominator == 0:
                slope = 0
            else:
                slope = numerator / denominator
            
            # 计算相关系数
            if NUMPY_AVAILABLE:
                correlation = float(np.corrcoef(x, values)[0, 1]) if len(set(values)) > 1 else 0.0
            else:
                # 简化的相关系数计算
                correlation = abs(slope) / (max(values) - min(values) + 1e-10)
                correlation = min(1.0, correlation)
            
            # 确定趋势方向
            if abs(slope) < 1e-6:
                direction = "stable"
            elif slope > 0:
                direction = "increasing"
            else:
                direction = "decreasing"
            
            # 趋势强度
            strength = abs(correlation)
            
            return {
                "direction": direction,
                "strength": strength,
                "slope": slope,
                "correlation": correlation
            }
            
        except Exception as e:
            logger.error(f"计算趋势失败: {e}")
            return {"direction": "unknown", "strength": 0.0}
    
    def _calculate_quality_metrics(self, comparison_results: List[Dict]) -> Dict:
        """计算质量指标"""
        try:
            quality_scores = []
            confidence_scores = []
            
            for result in comparison_results:
                # 提取质量相关指标
                overall_similarity = result.get("overall_similarity", 0.0)
                
                # 计算质量分数（基于相似度和差异数量）
                differences = result.get("differences", {})
                total_differences = (
                    len(differences.get("critical_differences", [])) +
                    len(differences.get("major_differences", [])) +
                    len(differences.get("minor_differences", []))
                )
                
                # 质量分数：相似度高且差异少表示质量好
                quality_score = overall_similarity * (1.0 - min(1.0, total_differences / 10))
                quality_scores.append(quality_score)
                
                # 置信度分数（基于各组件的成功率）
                component_success = 0
                total_components = 0
                
                for comp_name in ["text_comparison", "layout_comparison", "financial_comparison", "semantic_comparison"]:
                    comp_result = result.get(comp_name, {})
                    if comp_result.get("success", True):
                        component_success += 1
                    total_components += 1
                
                confidence = component_success / total_components if total_components > 0 else 0.0
                confidence_scores.append(confidence)
            
            # 计算质量统计
            quality_stats = self._calculate_descriptive_stats(quality_scores)
            confidence_stats = self._calculate_descriptive_stats(confidence_scores)
            
            return {
                "quality_statistics": quality_stats,
                "confidence_statistics": confidence_stats,
                "overall_quality_score": quality_stats.get("mean", 0.0),
                "overall_confidence_score": confidence_stats.get("mean", 0.0)
            }
            
        except Exception as e:
            logger.error(f"计算质量指标失败: {e}")
            return {}
    
    def _get_analysis_period(self, comparison_results: List[Dict]) -> Dict:
        """获取分析时间段"""
        try:
            timestamps = []
            
            for result in comparison_results:
                timestamp_str = result.get("timestamp")
                if timestamp_str:
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                        timestamps.append(timestamp)
                    except ValueError:
                        continue
            
            if not timestamps:
                return {"start": None, "end": None, "duration": None}
            
            start_time = min(timestamps)
            end_time = max(timestamps)
            duration = (end_time - start_time).total_seconds()
            
            return {
                "start": start_time.isoformat(),
                "end": end_time.isoformat(),
                "duration_seconds": duration,
                "duration_hours": duration / 3600
            }
            
        except Exception as e:
            logger.error(f"获取分析时间段失败: {e}")
            return {"start": None, "end": None, "duration": None}
    
    def generate_summary_report(self, statistics: Dict) -> str:
        """生成统计摘要报告"""
        try:
            summary = statistics.get("summary", {})
            basic_stats = statistics.get("basic_statistics", {})
            similarity_dist = statistics.get("similarity_distribution", {})
            
            total_comparisons = summary.get("total_comparisons", 0)
            success_rate = basic_stats.get("success_rate", 0.0)
            
            similarity_stats = basic_stats.get("similarity_statistics", {})
            avg_similarity = similarity_stats.get("mean", 0.0)
            
            distribution = similarity_dist.get("distribution", {})
            
            report_lines = [
                "比对统计摘要报告",
                "=" * 40,
                f"总比对次数: {total_comparisons}",
                f"成功率: {success_rate:.1%}",
                f"平均相似度: {avg_similarity:.1%}",
                "",
                "相似度分布:",
                f"  极高 (90%+): {distribution.get('very_high', 0):.1f}%",
                f"  高 (80-90%): {distribution.get('high', 0):.1f}%",
                f"  中等 (60-80%): {distribution.get('medium', 0):.1f}%",
                f"  低 (40-60%): {distribution.get('low', 0):.1f}%",
                f"  极低 (<40%): {distribution.get('very_low', 0):.1f}%",
                "",
                f"生成时间: {summary.get('generated_at', 'N/A')}"
            ]
            
            return "\n".join(report_lines)
            
        except Exception as e:
            logger.error(f"生成统计摘要报告失败: {e}")
            return f"报告生成失败: {str(e)}"
