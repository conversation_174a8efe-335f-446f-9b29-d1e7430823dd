"""
百度文心API客户端
"""
import base64
import asyncio
import time
from typing import Dict, List, Optional, Any
import httpx
from loguru import logger

from config.settings import settings


class BaiduClient:
    """百度文心API客户端"""
    
    def __init__(self):
        self.api_key = settings.baidu_api_key
        self.secret_key = settings.baidu_secret_key
        self.base_url = "https://aip.baidubce.com"
        self.model = "ernie-vision-8k"
        self.timeout = 60
        self.max_retries = 3
        self.access_token = None
        self.token_expires_at = 0
        
        if not self.api_key or not self.secret_key:
            logger.warning("百度API密钥未配置")
    
    async def analyze_image(self, image_path: str, prompt: str, **kwargs) -> Dict:
        """
        分析图像
        
        Args:
            image_path: 图像文件路径
            prompt: 分析提示
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        try:
            if not self.api_key or not self.secret_key:
                raise ValueError("百度API密钥未配置")
            
            # 获取访问令牌
            await self._ensure_access_token()
            
            # 编码图像
            image_base64 = self._encode_image(image_path)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image",
                            "image": image_base64
                        }
                    ]
                }
            ]
            
            # 发送请求
            response = await self._make_request(messages, **kwargs)
            
            if response.get("success", False):
                logger.info(f"百度文心图像分析完成: {image_path}")
            else:
                logger.error(f"百度文心图像分析失败: {response.get('error', '未知错误')}")
            
            return response
            
        except Exception as e:
            logger.error(f"百度文心图像分析异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    async def analyze_table_structure(self, image_path: str, **kwargs) -> Dict:
        """分析表格结构"""
        prompt = """请分析这张图片中的表格结构，识别表格的行列结构、单元格内容和数据类型。

分析要求：
1. 识别表格的行数和列数
2. 提取每个单元格的文本内容
3. 判断是否有表头
4. 识别合并单元格
5. 判断数据类型（文本、数字、金额、日期等）

请以JSON格式返回结构化的表格数据：
{
  "table_detected": true/false,
  "tables": [
    {
      "rows": 行数,
      "columns": 列数,
      "has_header": true/false,
      "cells": [
        {
          "row": 行号,
          "col": 列号,
          "text": "内容",
          "type": "数据类型"
        }
      ]
    }
  ]
}"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def extract_financial_data(self, image_path: str, **kwargs) -> Dict:
        """提取财务数据"""
        prompt = """请从这张图片中提取所有财务相关的数据，包括金额、数量、日期、财务术语等。

提取要求：
1. 识别所有金额数字和货币符号
2. 提取数量和对应的单位
3. 识别日期信息
4. 提取财务专业术语
5. 验证计算关系的正确性

请以JSON格式返回提取的财务数据。"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def compare_content_semantically(self, content1: str, content2: str, **kwargs) -> Dict:
        """语义比对内容"""
        prompt = f"""请对以下两段内容进行语义比对分析：

内容1：
{content1}

内容2：
{content2}

请分析两段内容的相似性和差异，重点关注：
1. 实质性内容差异
2. 金额和数量的一致性
3. 关键信息的匹配程度
4. 风险评估和建议

请返回JSON格式的详细比对结果。"""
        
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return await self._make_request(messages, **kwargs)
    
    async def _ensure_access_token(self):
        """确保访问令牌有效"""
        try:
            current_time = time.time()
            
            # 检查令牌是否过期
            if self.access_token and current_time < self.token_expires_at:
                return
            
            # 获取新的访问令牌
            url = f"{self.base_url}/oauth/2.0/token"
            params = {
                "grant_type": "client_credentials",
                "client_id": self.api_key,
                "client_secret": self.secret_key
            }
            
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.post(url, params=params)
                
                if response.status_code == 200:
                    result = response.json()
                    self.access_token = result.get("access_token")
                    expires_in = result.get("expires_in", 3600)
                    self.token_expires_at = current_time + expires_in - 300  # 提前5分钟刷新
                    
                    logger.info("百度访问令牌获取成功")
                else:
                    raise Exception(f"获取访问令牌失败: {response.status_code} - {response.text}")
                    
        except Exception as e:
            logger.error(f"获取百度访问令牌失败: {e}")
            raise
    
    def _encode_image(self, image_path: str) -> str:
        """编码图像为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"图像编码失败: {e}")
            raise
    
    async def _make_request(self, messages: List[Dict], **kwargs) -> Dict:
        """发送API请求"""
        try:
            if not self.access_token:
                await self._ensure_access_token()
            
            url = f"{self.base_url}/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/{self.model}"
            params = {"access_token": self.access_token}
            
            headers = {
                "Content-Type": "application/json"
            }
            
            data = {
                "messages": messages,
                "max_output_tokens": kwargs.get("max_tokens", 4000),
                "temperature": kwargs.get("temperature", 0.1),
                "top_p": kwargs.get("top_p", 0.95),
                "stream": False
            }
            
            # 重试机制
            for attempt in range(self.max_retries):
                try:
                    async with httpx.AsyncClient(timeout=self.timeout) as client:
                        response = await client.post(
                            url,
                            params=params,
                            headers=headers,
                            json=data
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            
                            if "result" in result:
                                content = result["result"]
                                usage = result.get("usage", {})
                                
                                return {
                                    "success": True,
                                    "content": content,
                                    "usage": usage,
                                    "model": self.model
                                }
                            else:
                                error_msg = result.get("error_msg", "响应格式错误")
                                return {
                                    "success": False,
                                    "error": error_msg,
                                    "content": "",
                                    "usage": {}
                                }
                        else:
                            error_msg = f"API请求失败: {response.status_code}"
                            if response.text:
                                error_msg += f" - {response.text}"
                            
                            # 如果是令牌过期，重新获取令牌
                            if response.status_code == 401:
                                self.access_token = None
                                await self._ensure_access_token()
                            
                            if attempt == self.max_retries - 1:
                                return {
                                    "success": False,
                                    "error": error_msg,
                                    "content": "",
                                    "usage": {}
                                }
                            else:
                                logger.warning(f"百度API请求失败，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                                await asyncio.sleep(2 ** attempt)
                                
                except httpx.TimeoutException:
                    error_msg = "API请求超时"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"百度API请求超时，重试 {attempt + 1}/{self.max_retries}")
                        await asyncio.sleep(2 ** attempt)
                        
                except Exception as e:
                    error_msg = f"API请求异常: {str(e)}"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"百度API请求异常，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        await asyncio.sleep(2 ** attempt)
            
            return {
                "success": False,
                "error": "所有重试均失败",
                "content": "",
                "usage": {}
            }
            
        except Exception as e:
            logger.error(f"发送百度API请求失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    def get_client_info(self) -> Dict:
        """获取客户端信息"""
        return {
            "provider": "Baidu",
            "model": self.model,
            "base_url": self.base_url,
            "api_key_configured": bool(self.api_key and self.secret_key),
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "access_token_valid": bool(self.access_token and time.time() < self.token_expires_at)
        }
