"""
大模型结果解析器
"""
import json
import re
from typing import Dict, List, Optional, Any, Union
from loguru import logger

from config.llm_config import RESULT_PARSER_CONFIG


class LLMResultParser:
    """大模型结果解析器"""
    
    def __init__(self):
        self.config = RESULT_PARSER_CONFIG
        self.strict_mode = self.config.get("json_validation", {}).get("strict_mode", True)
        self.auto_fix = self.config.get("json_validation", {}).get("auto_fix", True)
        self.max_attempts = self.config.get("error_handling", {}).get("max_parse_attempts", 3)
    
    def parse_table_analysis_result(self, raw_result: Dict) -> Dict:
        """
        解析表格分析结果
        
        Args:
            raw_result: 原始LLM返回结果
            
        Returns:
            解析后的结构化结果
        """
        try:
            content = raw_result.get("content", "")
            if not content:
                return self._create_error_result("空响应内容")
            
            # 尝试解析JSON
            parsed_data = self._parse_json_content(content)
            if not parsed_data:
                return self._create_error_result("JSON解析失败")
            
            # 验证表格分析结果结构
            validated_result = self._validate_table_analysis_structure(parsed_data)
            
            # 添加元数据
            validated_result["metadata"] = {
                "provider": raw_result.get("router_info", {}).get("provider", "unknown"),
                "model": raw_result.get("model", "unknown"),
                "response_time": raw_result.get("router_info", {}).get("response_time", 0),
                "usage": raw_result.get("usage", {}),
                "confidence": self._calculate_overall_confidence(validated_result)
            }
            
            logger.info("表格分析结果解析完成")
            return validated_result
            
        except Exception as e:
            logger.error(f"表格分析结果解析失败: {e}")
            return self._create_error_result(str(e))
    
    def parse_financial_extraction_result(self, raw_result: Dict) -> Dict:
        """
        解析财务数据提取结果
        
        Args:
            raw_result: 原始LLM返回结果
            
        Returns:
            解析后的结构化结果
        """
        try:
            content = raw_result.get("content", "")
            if not content:
                return self._create_error_result("空响应内容")
            
            # 解析JSON
            parsed_data = self._parse_json_content(content)
            if not parsed_data:
                return self._create_error_result("JSON解析失败")
            
            # 验证财务数据结构
            validated_result = self._validate_financial_data_structure(parsed_data)
            
            # 添加元数据
            validated_result["metadata"] = {
                "provider": raw_result.get("router_info", {}).get("provider", "unknown"),
                "model": raw_result.get("model", "unknown"),
                "response_time": raw_result.get("router_info", {}).get("response_time", 0),
                "usage": raw_result.get("usage", {}),
                "confidence": self._calculate_financial_confidence(validated_result)
            }
            
            logger.info("财务数据提取结果解析完成")
            return validated_result
            
        except Exception as e:
            logger.error(f"财务数据提取结果解析失败: {e}")
            return self._create_error_result(str(e))
    
    def parse_semantic_comparison_result(self, raw_result: Dict) -> Dict:
        """
        解析语义比对结果
        
        Args:
            raw_result: 原始LLM返回结果
            
        Returns:
            解析后的结构化结果
        """
        try:
            content = raw_result.get("content", "")
            if not content:
                return self._create_error_result("空响应内容")
            
            # 解析JSON
            parsed_data = self._parse_json_content(content)
            if not parsed_data:
                return self._create_error_result("JSON解析失败")
            
            # 验证语义比对结构
            validated_result = self._validate_semantic_comparison_structure(parsed_data)
            
            # 添加元数据
            validated_result["metadata"] = {
                "provider": raw_result.get("router_info", {}).get("provider", "unknown"),
                "model": raw_result.get("model", "unknown"),
                "response_time": raw_result.get("router_info", {}).get("response_time", 0),
                "usage": raw_result.get("usage", {}),
                "confidence": validated_result.get("confidence", 0.0)
            }
            
            logger.info("语义比对结果解析完成")
            return validated_result
            
        except Exception as e:
            logger.error(f"语义比对结果解析失败: {e}")
            return self._create_error_result(str(e))
    
    def _parse_json_content(self, content: str) -> Optional[Dict]:
        """解析JSON内容"""
        for attempt in range(self.max_attempts):
            try:
                # 清理内容
                cleaned_content = self._clean_json_content(content)
                
                # 尝试解析
                return json.loads(cleaned_content)
                
            except json.JSONDecodeError as e:
                logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{self.max_attempts}): {e}")
                
                if self.auto_fix and attempt < self.max_attempts - 1:
                    # 尝试修复JSON
                    content = self._fix_json_content(content, str(e))
                else:
                    break
                    
            except Exception as e:
                logger.error(f"JSON解析异常: {e}")
                break
        
        # 如果JSON解析失败，尝试提取结构化信息
        if self.config.get("error_handling", {}).get("fallback_to_text", True):
            return self._extract_structured_info_from_text(content)
        
        return None
    
    def _clean_json_content(self, content: str) -> str:
        """清理JSON内容"""
        try:
            # 移除markdown代码块标记
            content = re.sub(r'```json\s*', '', content)
            content = re.sub(r'```\s*$', '', content)
            
            # 移除前后空白
            content = content.strip()
            
            # 查找JSON对象
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                content = json_match.group(0)
            
            return content
            
        except Exception as e:
            logger.error(f"清理JSON内容失败: {e}")
            return content
    
    def _fix_json_content(self, content: str, error_msg: str) -> str:
        """修复JSON内容"""
        try:
            # 常见的JSON错误修复
            fixes = [
                # 修复尾随逗号
                (r',\s*}', '}'),
                (r',\s*]', ']'),
                # 修复单引号
                (r"'([^']*)':", r'"\1":'),
                # 修复未引用的键
                (r'(\w+):', r'"\1":'),
                # 修复换行符
                (r'\n', '\\n'),
                # 修复制表符
                (r'\t', '\\t'),
            ]
            
            for pattern, replacement in fixes:
                content = re.sub(pattern, replacement, content)
            
            return content
            
        except Exception as e:
            logger.error(f"修复JSON内容失败: {e}")
            return content
    
    def _extract_structured_info_from_text(self, content: str) -> Dict:
        """从文本中提取结构化信息"""
        try:
            # 基本的文本解析逻辑
            result = {
                "success": True,
                "parsed_from_text": True,
                "raw_content": content,
                "extracted_info": {}
            }
            
            # 提取数字
            numbers = re.findall(r'\d+\.?\d*', content)
            if numbers:
                result["extracted_info"]["numbers"] = numbers
            
            # 提取货币
            currencies = re.findall(r'[¥$€£]\s*\d+\.?\d*', content)
            if currencies:
                result["extracted_info"]["currencies"] = currencies
            
            # 提取日期
            dates = re.findall(r'\d{4}[-/]\d{1,2}[-/]\d{1,2}', content)
            if dates:
                result["extracted_info"]["dates"] = dates
            
            return result
            
        except Exception as e:
            logger.error(f"从文本提取结构化信息失败: {e}")
            return {"success": False, "error": str(e)}
    
    def _validate_table_analysis_structure(self, data: Dict) -> Dict:
        """验证表格分析结构"""
        try:
            validated = {
                "success": True,
                "table_detected": data.get("table_detected", False),
                "table_count": data.get("table_count", 0),
                "tables": [],
                "validation_errors": []
            }
            
            # 验证表格数据
            tables_data = data.get("tables", [])
            if isinstance(tables_data, list):
                for i, table in enumerate(tables_data):
                    validated_table = self._validate_single_table(table, i)
                    validated["tables"].append(validated_table)
            else:
                validated["validation_errors"].append("tables字段不是列表类型")
            
            return validated
            
        except Exception as e:
            logger.error(f"验证表格分析结构失败: {e}")
            return self._create_error_result(str(e))
    
    def _validate_single_table(self, table_data: Dict, table_index: int) -> Dict:
        """验证单个表格数据"""
        try:
            validated_table = {
                "table_id": table_data.get("table_id", table_index),
                "rows": max(0, int(table_data.get("rows", 0))),
                "columns": max(0, int(table_data.get("columns", 0))),
                "has_header": bool(table_data.get("has_header", False)),
                "cells": [],
                "confidence": float(table_data.get("confidence", 0.0)),
                "validation_errors": []
            }
            
            # 验证单元格数据
            cells_data = table_data.get("cells", [])
            if isinstance(cells_data, list):
                for cell in cells_data:
                    validated_cell = self._validate_table_cell(cell)
                    validated_table["cells"].append(validated_cell)
            else:
                validated_table["validation_errors"].append("cells字段不是列表类型")
            
            return validated_table
            
        except Exception as e:
            logger.error(f"验证表格数据失败: {e}")
            return {"validation_errors": [str(e)]}
    
    def _validate_table_cell(self, cell_data: Dict) -> Dict:
        """验证表格单元格数据"""
        try:
            return {
                "row": max(0, int(cell_data.get("row", 0))),
                "col": max(0, int(cell_data.get("col", 0))),
                "text": str(cell_data.get("text", "")),
                "data_type": cell_data.get("data_type", "text"),
                "confidence": float(cell_data.get("confidence", 0.0)),
                "is_merged": bool(cell_data.get("is_merged", False)),
                "rowspan": max(1, int(cell_data.get("rowspan", 1))),
                "colspan": max(1, int(cell_data.get("colspan", 1)))
            }
        except Exception as e:
            logger.error(f"验证单元格数据失败: {e}")
            return {"text": "", "validation_error": str(e)}
    
    def _validate_financial_data_structure(self, data: Dict) -> Dict:
        """验证财务数据结构"""
        try:
            financial_data = data.get("financial_data", data.get("financial_analysis", {}))
            
            validated = {
                "success": True,
                "amounts": self._validate_amounts(financial_data.get("amounts", [])),
                "quantities": self._validate_quantities(financial_data.get("quantities", [])),
                "dates": self._validate_dates(financial_data.get("dates", [])),
                "financial_terms": self._validate_financial_terms(financial_data.get("financial_terms", [])),
                "calculations": self._validate_calculations(financial_data.get("calculations", [])),
                "summary": financial_data.get("summary", {}),
                "validation_errors": []
            }
            
            return validated
            
        except Exception as e:
            logger.error(f"验证财务数据结构失败: {e}")
            return self._create_error_result(str(e))
    
    def _validate_amounts(self, amounts_data: List) -> List[Dict]:
        """验证金额数据"""
        validated_amounts = []
        
        for amount in amounts_data:
            try:
                validated_amount = {
                    "value": str(amount.get("value", "")),
                    "numeric_value": float(amount.get("numeric_value", 0)),
                    "currency": amount.get("currency", ""),
                    "formatted": amount.get("formatted", ""),
                    "category": amount.get("category", ""),
                    "confidence": float(amount.get("confidence", 0.0))
                }
                validated_amounts.append(validated_amount)
            except Exception as e:
                logger.warning(f"验证金额数据失败: {e}")
                
        return validated_amounts
    
    def _validate_quantities(self, quantities_data: List) -> List[Dict]:
        """验证数量数据"""
        validated_quantities = []
        
        for quantity in quantities_data:
            try:
                validated_quantity = {
                    "value": str(quantity.get("value", "")),
                    "numeric_value": float(quantity.get("numeric_value", 0)),
                    "unit": quantity.get("unit", ""),
                    "item_name": quantity.get("item_name", ""),
                    "confidence": float(quantity.get("confidence", 0.0))
                }
                validated_quantities.append(validated_quantity)
            except Exception as e:
                logger.warning(f"验证数量数据失败: {e}")
                
        return validated_quantities
    
    def _validate_dates(self, dates_data: List) -> List[Dict]:
        """验证日期数据"""
        validated_dates = []
        
        for date in dates_data:
            try:
                validated_date = {
                    "value": str(date.get("value", "")),
                    "parsed_date": date.get("parsed_date", ""),
                    "date_type": date.get("date_type", ""),
                    "confidence": float(date.get("confidence", 0.0))
                }
                validated_dates.append(validated_date)
            except Exception as e:
                logger.warning(f"验证日期数据失败: {e}")
                
        return validated_dates
    
    def _validate_financial_terms(self, terms_data: List) -> List[Dict]:
        """验证财务术语数据"""
        validated_terms = []
        
        for term in terms_data:
            try:
                validated_term = {
                    "term": str(term.get("term", "")),
                    "category": term.get("category", ""),
                    "value": term.get("value", ""),
                    "description": term.get("description", "")
                }
                validated_terms.append(validated_term)
            except Exception as e:
                logger.warning(f"验证财务术语失败: {e}")
                
        return validated_terms
    
    def _validate_calculations(self, calculations_data: List) -> List[Dict]:
        """验证计算数据"""
        validated_calculations = []
        
        for calc in calculations_data:
            try:
                validated_calc = {
                    "type": calc.get("type", ""),
                    "formula": calc.get("formula", ""),
                    "result": calc.get("result", ""),
                    "is_correct": bool(calc.get("is_correct", False)),
                    "description": calc.get("description", "")
                }
                validated_calculations.append(validated_calc)
            except Exception as e:
                logger.warning(f"验证计算数据失败: {e}")
                
        return validated_calculations
    
    def _validate_semantic_comparison_structure(self, data: Dict) -> Dict:
        """验证语义比对结构"""
        try:
            comparison_data = data.get("comparison_analysis", data.get("semantic_comparison", {}))
            
            validated = {
                "success": True,
                "overall_similarity": float(comparison_data.get("overall_similarity", 0.0)),
                "content_match": bool(comparison_data.get("content_match", False)),
                "differences": comparison_data.get("detailed_differences", comparison_data.get("key_differences", [])),
                "identical_elements": comparison_data.get("identical_elements", []),
                "financial_consistency": comparison_data.get("financial_consistency", {}),
                "recommendation": data.get("final_recommendation", data.get("recommendation", {})),
                "confidence": float(data.get("confidence", 0.0)),
                "validation_errors": []
            }
            
            return validated
            
        except Exception as e:
            logger.error(f"验证语义比对结构失败: {e}")
            return self._create_error_result(str(e))
    
    def _calculate_overall_confidence(self, result: Dict) -> float:
        """计算整体置信度"""
        try:
            confidences = []
            
            # 收集表格置信度
            for table in result.get("tables", []):
                table_confidence = table.get("confidence", 0.0)
                if table_confidence > 0:
                    confidences.append(table_confidence)
                
                # 收集单元格置信度
                for cell in table.get("cells", []):
                    cell_confidence = cell.get("confidence", 0.0)
                    if cell_confidence > 0:
                        confidences.append(cell_confidence)
            
            if confidences:
                return sum(confidences) / len(confidences)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算整体置信度失败: {e}")
            return 0.0
    
    def _calculate_financial_confidence(self, result: Dict) -> float:
        """计算财务数据置信度"""
        try:
            confidences = []
            
            # 收集各类数据的置信度
            for amounts in result.get("amounts", []):
                if amounts.get("confidence", 0) > 0:
                    confidences.append(amounts["confidence"])
            
            for quantities in result.get("quantities", []):
                if quantities.get("confidence", 0) > 0:
                    confidences.append(quantities["confidence"])
            
            for dates in result.get("dates", []):
                if dates.get("confidence", 0) > 0:
                    confidences.append(dates["confidence"])
            
            if confidences:
                return sum(confidences) / len(confidences)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算财务数据置信度失败: {e}")
            return 0.0
    
    def _create_error_result(self, error_message: str) -> Dict:
        """创建错误结果"""
        return {
            "success": False,
            "error": error_message,
            "data": {},
            "confidence": 0.0,
            "validation_errors": [error_message]
        }
