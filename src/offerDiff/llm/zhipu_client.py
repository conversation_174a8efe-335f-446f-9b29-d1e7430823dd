"""
智谱AI API客户端
"""
import base64
import asyncio
from typing import Dict, List, Optional, Any
import httpx
from loguru import logger

from config.settings import settings


class ZhipuClient:
    """智谱AI API客户端"""
    
    def __init__(self):
        self.api_key = settings.zhipu_api_key
        self.base_url = "https://open.bigmodel.cn/api/paas/v4"
        self.model = "glm-4v-plus"
        self.timeout = 60
        self.max_retries = 3
        
        if not self.api_key:
            logger.warning("智谱AI API密钥未配置")
    
    async def analyze_image(self, image_path: str, prompt: str, **kwargs) -> Dict:
        """
        分析图像
        
        Args:
            image_path: 图像文件路径
            prompt: 分析提示
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        try:
            if not self.api_key:
                raise ValueError("智谱AI API密钥未配置")
            
            # 编码图像
            image_base64 = self._encode_image(image_path)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 发送请求
            response = await self._make_request(messages, **kwargs)
            
            if response.get("success", False):
                logger.info(f"智谱AI图像分析完成: {image_path}")
            else:
                logger.error(f"智谱AI图像分析失败: {response.get('error', '未知错误')}")
            
            return response
            
        except Exception as e:
            logger.error(f"智谱AI图像分析异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    async def analyze_table_structure(self, image_path: str, **kwargs) -> Dict:
        """分析表格结构"""
        prompt = """请仔细分析这张图片中的表格结构，识别所有表格元素并返回JSON格式的结构化数据。

要求：
1. 准确识别表格边界和行列结构
2. 提取每个单元格的文本内容
3. 识别表头和数据行
4. 检测合并单元格
5. 判断数据类型（文本、数字、金额、日期等）

请按以下JSON格式返回：
{
  "tables": [
    {
      "id": 1,
      "rows": 行数,
      "columns": 列数,
      "has_header": true/false,
      "cells": [
        {
          "row": 行号,
          "col": 列号,
          "text": "内容",
          "type": "数据类型",
          "confidence": 置信度
        }
      ]
    }
  ]
}"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def extract_financial_data(self, image_path: str, **kwargs) -> Dict:
        """提取财务数据"""
        prompt = """请从图片中提取所有财务相关数据，包括金额、数量、日期等，并返回JSON格式的结构化结果。

重点关注：
1. 所有金额数字和货币符号
2. 数量和单位
3. 日期信息
4. 财务术语
5. 计算关系验证

请按JSON格式返回提取结果。"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def compare_content_semantically(self, content1: str, content2: str, **kwargs) -> Dict:
        """语义比对内容"""
        prompt = f"""请对以下两段内容进行语义比对分析：

内容1：
{content1}

内容2：
{content2}

请分析两段内容的相似性和差异，重点关注：
1. 实质性内容差异
2. 金额和数量的一致性
3. 关键信息的匹配程度
4. 风险评估和建议

请返回JSON格式的比对结果。"""
        
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return await self._make_request(messages, **kwargs)
    
    def _encode_image(self, image_path: str) -> str:
        """编码图像为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"图像编码失败: {e}")
            raise
    
    async def _make_request(self, messages: List[Dict], **kwargs) -> Dict:
        """发送API请求"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", 4000),
                "temperature": kwargs.get("temperature", 0.1),
                "top_p": kwargs.get("top_p", 0.95),
                "stream": False
            }
            
            # 重试机制
            for attempt in range(self.max_retries):
                try:
                    async with httpx.AsyncClient(timeout=self.timeout) as client:
                        response = await client.post(
                            f"{self.base_url}/chat/completions",
                            headers=headers,
                            json=data
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            
                            if "choices" in result and result["choices"]:
                                content = result["choices"][0]["message"]["content"]
                                usage = result.get("usage", {})
                                
                                return {
                                    "success": True,
                                    "content": content,
                                    "usage": usage,
                                    "model": data["model"]
                                }
                            else:
                                return {
                                    "success": False,
                                    "error": "响应格式错误",
                                    "content": "",
                                    "usage": {}
                                }
                        else:
                            error_msg = f"API请求失败: {response.status_code}"
                            if response.text:
                                error_msg += f" - {response.text}"
                            
                            if attempt == self.max_retries - 1:
                                return {
                                    "success": False,
                                    "error": error_msg,
                                    "content": "",
                                    "usage": {}
                                }
                            else:
                                logger.warning(f"智谱AI请求失败，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                                await asyncio.sleep(2 ** attempt)
                                
                except httpx.TimeoutException:
                    error_msg = "API请求超时"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"智谱AI请求超时，重试 {attempt + 1}/{self.max_retries}")
                        await asyncio.sleep(2 ** attempt)
                        
                except Exception as e:
                    error_msg = f"API请求异常: {str(e)}"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"智谱AI请求异常，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        await asyncio.sleep(2 ** attempt)
            
            return {
                "success": False,
                "error": "所有重试均失败",
                "content": "",
                "usage": {}
            }
            
        except Exception as e:
            logger.error(f"发送智谱AI请求失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    def get_client_info(self) -> Dict:
        """获取客户端信息"""
        return {
            "provider": "ZhipuAI",
            "model": self.model,
            "base_url": self.base_url,
            "api_key_configured": bool(self.api_key),
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }
