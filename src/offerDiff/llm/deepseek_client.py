"""
DeepSeek API客户端
"""
import base64
import asyncio
from typing import Dict, List, Optional, Any
import httpx
from loguru import logger

from config.settings import settings


class DeepSeekClient:
    """DeepSeek API客户端"""
    
    def __init__(self):
        self.api_key = settings.deepseek_api_key
        self.base_url = settings.deepseek_base_url
        self.model = "deepseek-vl-7b-chat"
        self.timeout = 60
        self.max_retries = 3
        
        if not self.api_key:
            logger.warning("DeepSeek API密钥未配置")
    
    async def analyze_image(self, image_path: str, prompt: str, **kwargs) -> Dict:
        """
        分析图像
        
        Args:
            image_path: 图像文件路径
            prompt: 分析提示
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        try:
            if not self.api_key:
                raise ValueError("DeepSeek API密钥未配置")
            
            # 编码图像
            image_base64 = self._encode_image(image_path)
            
            # 构建请求
            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_base64}"
                            }
                        }
                    ]
                }
            ]
            
            # 发送请求
            response = await self._make_request(messages, **kwargs)
            
            if response.get("success", False):
                logger.info(f"DeepSeek图像分析完成: {image_path}")
            else:
                logger.error(f"DeepSeek图像分析失败: {response.get('error', '未知错误')}")
            
            return response
            
        except Exception as e:
            logger.error(f"DeepSeek图像分析异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    async def analyze_table_structure(self, image_path: str, **kwargs) -> Dict:
        """
        分析表格结构
        
        Args:
            image_path: 图像文件路径
            **kwargs: 其他参数
            
        Returns:
            表格结构分析结果
        """
        prompt = """请分析这张图片中的表格结构，并按以下格式返回JSON结果：

{
  "table_detected": true/false,
  "table_count": 表格数量,
  "tables": [
    {
      "table_id": 1,
      "rows": 行数,
      "columns": 列数,
      "has_header": true/false,
      "header_row": 表头行号,
      "cells": [
        {
          "row": 行号,
          "col": 列号,
          "text": "单元格内容",
          "is_merged": true/false,
          "data_type": "text/number/currency/date"
        }
      ],
      "table_type": "financial/data/form",
      "confidence": 0.95
    }
  ]
}

请仔细识别表格中的所有文本内容，特别注意：
1. 数字和金额的准确识别
2. 表格边界和单元格分割
3. 合并单元格的检测
4. 表头的识别
5. 数据类型的判断

只返回JSON格式的结果，不要添加其他说明文字。"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def extract_financial_data(self, image_path: str, **kwargs) -> Dict:
        """
        提取财务数据
        
        Args:
            image_path: 图像文件路径
            **kwargs: 其他参数
            
        Returns:
            财务数据提取结果
        """
        prompt = """请从这张图片中提取所有财务相关的数据，并按以下格式返回JSON结果：

{
  "financial_data": {
    "amounts": [
      {
        "value": "金额数值",
        "currency": "货币符号",
        "formatted": "格式化显示",
        "position": "在文档中的位置描述",
        "context": "金额的上下文说明"
      }
    ],
    "quantities": [
      {
        "value": "数量",
        "unit": "单位",
        "item": "物品名称",
        "position": "位置描述"
      }
    ],
    "percentages": [
      {
        "value": "百分比数值",
        "context": "百分比说明",
        "position": "位置描述"
      }
    ],
    "dates": [
      {
        "value": "日期",
        "format": "日期格式",
        "context": "日期说明",
        "position": "位置描述"
      }
    ],
    "financial_terms": [
      {
        "term": "财务术语",
        "category": "术语类别",
        "position": "位置描述"
      }
    ]
  },
  "summary": {
    "total_amounts": 金额总数,
    "currency_types": ["货币类型列表"],
    "has_calculations": true/false,
    "document_type": "invoice/contract/report/other"
  },
  "confidence": 0.95
}

请特别注意：
1. 准确识别所有数字和金额
2. 区分不同的货币符号
3. 识别计算关系（如小计、合计）
4. 提取相关的财务术语
5. 判断文档类型

只返回JSON格式的结果。"""
        
        return await self.analyze_image(image_path, prompt, **kwargs)
    
    async def compare_content_semantically(self, content1: str, content2: str, **kwargs) -> Dict:
        """
        语义比对内容
        
        Args:
            content1: 第一个内容
            content2: 第二个内容
            **kwargs: 其他参数
            
        Returns:
            语义比对结果
        """
        prompt = f"""请对以下两段内容进行语义比对分析，并按JSON格式返回结果：

内容1：
{content1}

内容2：
{content2}

请按以下格式返回分析结果：

{{
  "semantic_comparison": {{
    "overall_similarity": 0.95,
    "content_match": true/false,
    "key_differences": [
      {{
        "type": "amount/quantity/term/structure",
        "description": "差异描述",
        "content1_value": "内容1中的值",
        "content2_value": "内容2中的值",
        "severity": "high/medium/low",
        "impact": "差异影响说明"
      }}
    ],
    "identical_elements": [
      {{
        "type": "element类型",
        "value": "相同的值",
        "confidence": 0.98
      }}
    ],
    "financial_consistency": {{
      "amounts_match": true/false,
      "calculations_consistent": true/false,
      "currency_consistent": true/false,
      "totals_match": true/false
    }},
    "structural_consistency": {{
      "format_similar": true/false,
      "organization_similar": true/false,
      "completeness_match": true/false
    }}
  }},
  "recommendation": {{
    "is_acceptable": true/false,
    "risk_level": "low/medium/high",
    "action_required": "none/review/reject",
    "explanation": "建议说明"
  }},
  "confidence": 0.95
}}

请重点关注：
1. 金额和数量的一致性
2. 关键财务数据的匹配
3. 实质性内容差异
4. 格式和结构差异的影响

只返回JSON格式的结果。"""
        
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        return await self._make_request(messages, **kwargs)
    
    def _encode_image(self, image_path: str) -> str:
        """编码图像为base64"""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            logger.error(f"图像编码失败: {e}")
            raise
    
    async def _make_request(self, messages: List[Dict], **kwargs) -> Dict:
        """发送API请求"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": kwargs.get("model", self.model),
                "messages": messages,
                "max_tokens": kwargs.get("max_tokens", 4000),
                "temperature": kwargs.get("temperature", 0.1),
                "top_p": kwargs.get("top_p", 0.95),
                "stream": False
            }
            
            # 重试机制
            for attempt in range(self.max_retries):
                try:
                    async with httpx.AsyncClient(timeout=self.timeout) as client:
                        response = await client.post(
                            f"{self.base_url}/chat/completions",
                            headers=headers,
                            json=data
                        )
                        
                        if response.status_code == 200:
                            result = response.json()
                            
                            if "choices" in result and result["choices"]:
                                content = result["choices"][0]["message"]["content"]
                                usage = result.get("usage", {})
                                
                                return {
                                    "success": True,
                                    "content": content,
                                    "usage": usage,
                                    "model": data["model"]
                                }
                            else:
                                return {
                                    "success": False,
                                    "error": "响应格式错误",
                                    "content": "",
                                    "usage": {}
                                }
                        else:
                            error_msg = f"API请求失败: {response.status_code}"
                            if response.text:
                                error_msg += f" - {response.text}"
                            
                            if attempt == self.max_retries - 1:
                                return {
                                    "success": False,
                                    "error": error_msg,
                                    "content": "",
                                    "usage": {}
                                }
                            else:
                                logger.warning(f"API请求失败，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                                await asyncio.sleep(2 ** attempt)  # 指数退避
                                
                except httpx.TimeoutException:
                    error_msg = "API请求超时"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"API请求超时，重试 {attempt + 1}/{self.max_retries}")
                        await asyncio.sleep(2 ** attempt)
                        
                except Exception as e:
                    error_msg = f"API请求异常: {str(e)}"
                    if attempt == self.max_retries - 1:
                        return {
                            "success": False,
                            "error": error_msg,
                            "content": "",
                            "usage": {}
                        }
                    else:
                        logger.warning(f"API请求异常，重试 {attempt + 1}/{self.max_retries}: {error_msg}")
                        await asyncio.sleep(2 ** attempt)
            
            return {
                "success": False,
                "error": "所有重试均失败",
                "content": "",
                "usage": {}
            }
            
        except Exception as e:
            logger.error(f"发送API请求失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    def get_client_info(self) -> Dict:
        """获取客户端信息"""
        return {
            "provider": "DeepSeek",
            "model": self.model,
            "base_url": self.base_url,
            "api_key_configured": bool(self.api_key),
            "timeout": self.timeout,
            "max_retries": self.max_retries
        }
