"""
大模型API路由器
"""
import asyncio
import random
import time
from typing import Dict, List, Optional, Any
from enum import Enum
from loguru import logger

from src.offerDiff.llm.deepseek_client import DeepSeekClient
from src.offerDiff.llm.zhipu_client import Zhipu<PERSON>lient
from src.offerDiff.llm.baidu_client import Baidu<PERSON>lient
from config.llm_config import API_ROUTER_CONFIG


class ProviderStatus(str, Enum):
    """服务提供商状态"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    CIRCUIT_OPEN = "circuit_open"


class LoadBalancingStrategy(str, Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    RANDOM = "random"
    WEIGHTED = "weighted"
    LEAST_USED = "least_used"


class LLMAPIRouter:
    """大模型API路由器"""
    
    def __init__(self):
        # 初始化客户端
        self.clients = {
            "deepseek": DeepSeekClient(),
            "zhipu": ZhipuClient(),
            "baidu": BaiduClient()
        }
        
        # 配置
        self.config = API_ROUTER_CONFIG
        self.default_provider = self.config["default_provider"]
        self.fallback_providers = self.config["fallback_providers"]
        
        # 状态跟踪
        self.provider_status = {
            provider: ProviderStatus.HEALTHY 
            for provider in self.clients.keys()
        }
        
        # 使用统计
        self.usage_stats = {
            provider: {
                "requests": 0,
                "successes": 0,
                "failures": 0,
                "last_used": 0,
                "avg_response_time": 0,
                "total_response_time": 0
            }
            for provider in self.clients.keys()
        }
        
        # 熔断器状态
        self.circuit_breaker = {
            provider: {
                "failure_count": 0,
                "last_failure_time": 0,
                "state": "closed",  # closed, open, half_open
                "next_attempt_time": 0
            }
            for provider in self.clients.keys()
        }
        
        # 负载均衡
        self.round_robin_index = 0
        
        logger.info("LLM API路由器初始化完成")
    
    async def analyze_image(self, image_path: str, prompt: str, 
                          preferred_provider: Optional[str] = None, **kwargs) -> Dict:
        """
        分析图像（带路由）
        
        Args:
            image_path: 图像文件路径
            prompt: 分析提示
            preferred_provider: 首选提供商
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        try:
            # 选择提供商
            provider = self._select_provider(preferred_provider, "vision")
            
            if not provider:
                return {
                    "success": False,
                    "error": "没有可用的提供商",
                    "content": "",
                    "usage": {}
                }
            
            # 执行请求
            start_time = time.time()
            result = await self._execute_with_fallback(
                "analyze_image", 
                provider, 
                image_path, 
                prompt, 
                **kwargs
            )
            
            # 更新统计
            response_time = time.time() - start_time
            self._update_stats(provider, result.get("success", False), response_time)
            
            # 添加路由信息
            result["router_info"] = {
                "provider": provider,
                "response_time": response_time,
                "fallback_used": result.get("fallback_used", False)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"图像分析路由失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    async def analyze_table_structure(self, image_path: str, 
                                    preferred_provider: Optional[str] = None, **kwargs) -> Dict:
        """分析表格结构（带路由）"""
        provider = self._select_provider(preferred_provider, "vision")
        
        if not provider:
            return {
                "success": False,
                "error": "没有可用的提供商",
                "content": "",
                "usage": {}
            }
        
        start_time = time.time()
        result = await self._execute_with_fallback(
            "analyze_table_structure", 
            provider, 
            image_path, 
            **kwargs
        )
        
        response_time = time.time() - start_time
        self._update_stats(provider, result.get("success", False), response_time)
        
        result["router_info"] = {
            "provider": provider,
            "response_time": response_time,
            "fallback_used": result.get("fallback_used", False)
        }
        
        return result
    
    async def extract_financial_data(self, image_path: str, 
                                   preferred_provider: Optional[str] = None, **kwargs) -> Dict:
        """提取财务数据（带路由）"""
        provider = self._select_provider(preferred_provider, "vision")
        
        if not provider:
            return {
                "success": False,
                "error": "没有可用的提供商",
                "content": "",
                "usage": {}
            }
        
        start_time = time.time()
        result = await self._execute_with_fallback(
            "extract_financial_data", 
            provider, 
            image_path, 
            **kwargs
        )
        
        response_time = time.time() - start_time
        self._update_stats(provider, result.get("success", False), response_time)
        
        result["router_info"] = {
            "provider": provider,
            "response_time": response_time,
            "fallback_used": result.get("fallback_used", False)
        }
        
        return result
    
    async def compare_content_semantically(self, content1: str, content2: str, 
                                         preferred_provider: Optional[str] = None, **kwargs) -> Dict:
        """语义比对内容（带路由）"""
        provider = self._select_provider(preferred_provider, "text")
        
        if not provider:
            return {
                "success": False,
                "error": "没有可用的提供商",
                "content": "",
                "usage": {}
            }
        
        start_time = time.time()
        result = await self._execute_with_fallback(
            "compare_content_semantically", 
            provider, 
            content1, 
            content2, 
            **kwargs
        )
        
        response_time = time.time() - start_time
        self._update_stats(provider, result.get("success", False), response_time)
        
        result["router_info"] = {
            "provider": provider,
            "response_time": response_time,
            "fallback_used": result.get("fallback_used", False)
        }
        
        return result
    
    def _select_provider(self, preferred_provider: Optional[str], task_type: str) -> Optional[str]:
        """选择提供商"""
        try:
            # 如果指定了首选提供商且可用，使用它
            if preferred_provider and self._is_provider_available(preferred_provider):
                return preferred_provider
            
            # 获取可用的提供商
            available_providers = [
                provider for provider in self.clients.keys()
                if self._is_provider_available(provider)
            ]
            
            if not available_providers:
                logger.error("没有可用的提供商")
                return None
            
            # 根据负载均衡策略选择
            strategy = self.config.get("load_balancing", {}).get("strategy", "round_robin")
            
            if strategy == "round_robin":
                return self._round_robin_select(available_providers)
            elif strategy == "random":
                return random.choice(available_providers)
            elif strategy == "weighted":
                return self._weighted_select(available_providers)
            elif strategy == "least_used":
                return self._least_used_select(available_providers)
            else:
                return available_providers[0]
                
        except Exception as e:
            logger.error(f"选择提供商失败: {e}")
            return None
    
    def _is_provider_available(self, provider: str) -> bool:
        """检查提供商是否可用"""
        try:
            # 检查熔断器状态
            circuit = self.circuit_breaker[provider]
            current_time = time.time()
            
            if circuit["state"] == "open":
                # 检查是否可以尝试半开状态
                if current_time >= circuit["next_attempt_time"]:
                    circuit["state"] = "half_open"
                    logger.info(f"提供商 {provider} 进入半开状态")
                else:
                    return False
            
            # 检查提供商状态
            status = self.provider_status[provider]
            return status in [ProviderStatus.HEALTHY, ProviderStatus.DEGRADED]
            
        except Exception as e:
            logger.error(f"检查提供商可用性失败: {e}")
            return False
    
    def _round_robin_select(self, providers: List[str]) -> str:
        """轮询选择"""
        if not providers:
            return None
        
        provider = providers[self.round_robin_index % len(providers)]
        self.round_robin_index += 1
        return provider
    
    def _weighted_select(self, providers: List[str]) -> str:
        """加权选择"""
        try:
            weights = self.config.get("load_balancing", {}).get("weights", {})
            
            # 计算权重
            weighted_providers = []
            for provider in providers:
                weight = weights.get(provider, 1.0)
                weighted_providers.extend([provider] * int(weight * 10))
            
            return random.choice(weighted_providers) if weighted_providers else providers[0]
            
        except Exception as e:
            logger.error(f"加权选择失败: {e}")
            return providers[0]
    
    def _least_used_select(self, providers: List[str]) -> str:
        """最少使用选择"""
        try:
            # 选择使用次数最少的提供商
            min_requests = float('inf')
            selected_provider = providers[0]
            
            for provider in providers:
                requests = self.usage_stats[provider]["requests"]
                if requests < min_requests:
                    min_requests = requests
                    selected_provider = provider
            
            return selected_provider
            
        except Exception as e:
            logger.error(f"最少使用选择失败: {e}")
            return providers[0]
    
    async def _execute_with_fallback(self, method_name: str, primary_provider: str, *args, **kwargs) -> Dict:
        """执行请求并处理降级"""
        try:
            # 尝试主要提供商
            result = await self._execute_request(method_name, primary_provider, *args, **kwargs)
            
            if result.get("success", False):
                # 成功，更新熔断器状态
                self._update_circuit_breaker(primary_provider, True)
                return result
            
            # 失败，更新熔断器状态
            self._update_circuit_breaker(primary_provider, False)
            
            # 尝试降级提供商
            for fallback_provider in self.fallback_providers:
                if fallback_provider != primary_provider and self._is_provider_available(fallback_provider):
                    logger.warning(f"使用降级提供商: {fallback_provider}")
                    
                    fallback_result = await self._execute_request(method_name, fallback_provider, *args, **kwargs)
                    
                    if fallback_result.get("success", False):
                        fallback_result["fallback_used"] = True
                        fallback_result["fallback_provider"] = fallback_provider
                        self._update_circuit_breaker(fallback_provider, True)
                        return fallback_result
                    
                    self._update_circuit_breaker(fallback_provider, False)
            
            # 所有提供商都失败
            return {
                "success": False,
                "error": "所有提供商都不可用",
                "content": "",
                "usage": {}
            }
            
        except Exception as e:
            logger.error(f"执行请求失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    async def _execute_request(self, method_name: str, provider: str, *args, **kwargs) -> Dict:
        """执行具体的请求"""
        try:
            client = self.clients[provider]
            method = getattr(client, method_name)
            
            if not method:
                raise AttributeError(f"提供商 {provider} 不支持方法 {method_name}")
            
            return await method(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"执行 {provider} 的 {method_name} 方法失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "content": "",
                "usage": {}
            }
    
    def _update_stats(self, provider: str, success: bool, response_time: float):
        """更新使用统计"""
        try:
            stats = self.usage_stats[provider]
            stats["requests"] += 1
            stats["last_used"] = time.time()
            stats["total_response_time"] += response_time
            stats["avg_response_time"] = stats["total_response_time"] / stats["requests"]
            
            if success:
                stats["successes"] += 1
            else:
                stats["failures"] += 1
                
        except Exception as e:
            logger.error(f"更新统计失败: {e}")
    
    def _update_circuit_breaker(self, provider: str, success: bool):
        """更新熔断器状态"""
        try:
            circuit = self.circuit_breaker[provider]
            current_time = time.time()
            
            if success:
                # 成功，重置失败计数
                circuit["failure_count"] = 0
                if circuit["state"] == "half_open":
                    circuit["state"] = "closed"
                    logger.info(f"提供商 {provider} 熔断器关闭")
            else:
                # 失败，增加失败计数
                circuit["failure_count"] += 1
                circuit["last_failure_time"] = current_time
                
                # 检查是否需要打开熔断器
                failure_threshold = self.config.get("circuit_breaker", {}).get("failure_threshold", 5)
                if circuit["failure_count"] >= failure_threshold:
                    circuit["state"] = "open"
                    recovery_timeout = self.config.get("circuit_breaker", {}).get("recovery_timeout", 300)
                    circuit["next_attempt_time"] = current_time + recovery_timeout
                    logger.warning(f"提供商 {provider} 熔断器打开")
                    
        except Exception as e:
            logger.error(f"更新熔断器状态失败: {e}")
    
    def get_router_status(self) -> Dict:
        """获取路由器状态"""
        return {
            "provider_status": dict(self.provider_status),
            "usage_stats": self.usage_stats,
            "circuit_breaker": self.circuit_breaker,
            "config": self.config
        }
    
    def get_health_status(self) -> Dict:
        """获取健康状态"""
        healthy_providers = [
            provider for provider, status in self.provider_status.items()
            if status == ProviderStatus.HEALTHY
        ]
        
        return {
            "healthy_providers": healthy_providers,
            "total_providers": len(self.clients),
            "health_ratio": len(healthy_providers) / len(self.clients),
            "default_provider_healthy": self.provider_status.get(self.default_provider) == ProviderStatus.HEALTHY
        }
