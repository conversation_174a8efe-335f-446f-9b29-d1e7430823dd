"""
提示模板管理
"""
from typing import Dict, List, Optional, Any
from loguru import logger


class PromptTemplates:
    """提示模板管理器"""
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, str]:
        """加载所有提示模板"""
        return {
            "table_structure_analysis": self._get_table_structure_template(),
            "financial_data_extraction": self._get_financial_extraction_template(),
            "content_understanding": self._get_content_understanding_template(),
            "semantic_comparison": self._get_semantic_comparison_template(),
            "document_classification": self._get_document_classification_template(),
            "quality_assessment": self._get_quality_assessment_template(),
            "layout_analysis": self._get_layout_analysis_template()
        }
    
    def get_template(self, template_name: str, **kwargs) -> str:
        """
        获取格式化的提示模板
        
        Args:
            template_name: 模板名称
            **kwargs: 模板参数
            
        Returns:
            格式化的提示文本
        """
        try:
            if template_name not in self.templates:
                raise ValueError(f"未找到模板: {template_name}")
            
            template = self.templates[template_name]
            
            # 格式化模板
            if kwargs:
                template = template.format(**kwargs)
            
            return template
            
        except Exception as e:
            logger.error(f"获取提示模板失败: {e}")
            return ""
    
    def _get_table_structure_template(self) -> str:
        """表格结构分析模板"""
        return """请仔细分析这张图片中的表格结构，并按以下JSON格式返回详细结果：

{{
  "analysis_result": {{
    "table_detected": true/false,
    "table_count": 表格数量,
    "image_quality": {{
      "clarity": "high/medium/low",
      "resolution": "sufficient/insufficient",
      "scan_quality": "good/fair/poor"
    }},
    "tables": [
      {{
        "table_id": 1,
        "position": {{
          "x": 左上角X坐标,
          "y": 左上角Y坐标,
          "width": 宽度,
          "height": 高度
        }},
        "structure": {{
          "rows": 行数,
          "columns": 列数,
          "has_header": true/false,
          "header_rows": 表头行数,
          "has_borders": true/false,
          "border_style": "solid/dashed/none"
        }},
        "cells": [
          {{
            "row": 行号(从0开始),
            "col": 列号(从0开始),
            "text": "单元格内容",
            "confidence": 0.95,
            "data_type": "text/number/currency/date/percentage",
            "is_merged": true/false,
            "rowspan": 跨行数,
            "colspan": 跨列数,
            "alignment": "left/center/right",
            "formatting": {{
              "bold": true/false,
              "italic": true/false,
              "font_size": "small/medium/large"
            }}
          }}
        ],
        "table_type": "financial/data/form/list",
        "content_category": "invoice/contract/report/price_list",
        "language": "chinese/english/mixed",
        "confidence": 0.95
      }}
    ]
  }},
  "processing_notes": [
    "处理过程中的注意事项或发现的问题"
  ]
}}

分析要求：
1. 准确识别表格边界和单元格分割
2. 正确判断表头位置和内容
3. 识别合并单元格的范围
4. 准确提取每个单元格的文本内容
5. 判断数据类型（特别是数字、金额、日期）
6. 评估识别的置信度
7. 注意表格的完整性和清晰度

特别注意：
- 数字和金额要完整准确
- 中文字符的正确识别
- 表格线条的完整性
- 单元格内容的对齐方式
- 可能存在的手写内容

只返回JSON格式的结果，不要添加其他说明文字。"""
    
    def _get_financial_extraction_template(self) -> str:
        """财务数据提取模板"""
        return """请从这张图片中提取所有财务相关的数据，并按以下JSON格式返回详细结果：

{{
  "financial_analysis": {{
    "document_type": "invoice/contract/quotation/receipt/report",
    "currency_info": {{
      "primary_currency": "CNY/USD/EUR",
      "currency_symbols": ["¥", "$", "€"],
      "exchange_rates": {{}}
    }},
    "amounts": [
      {{
        "value": "原始金额文本",
        "numeric_value": 数值,
        "currency": "货币符号",
        "formatted": "标准格式化显示",
        "category": "unit_price/subtotal/total/tax/discount",
        "description": "金额说明",
        "position": {{
          "row": 行号,
          "col": 列号,
          "context": "周围文本上下文"
        }},
        "confidence": 0.95
      }}
    ],
    "quantities": [
      {{
        "value": "数量文本",
        "numeric_value": 数值,
        "unit": "单位",
        "item_name": "物品名称",
        "item_description": "物品描述",
        "position": {{
          "row": 行号,
          "col": 列号
        }},
        "confidence": 0.95
      }}
    ],
    "calculations": [
      {{
        "type": "multiplication/addition/subtraction",
        "formula": "计算公式",
        "operands": ["操作数列表"],
        "result": "计算结果",
        "is_correct": true/false,
        "description": "计算说明"
      }}
    ],
    "dates": [
      {{
        "value": "日期文本",
        "parsed_date": "2024-01-01",
        "date_type": "invoice_date/due_date/delivery_date",
        "format": "YYYY-MM-DD/DD/MM/YYYY",
        "confidence": 0.95
      }}
    ],
    "financial_terms": [
      {{
        "term": "财务术语",
        "category": "tax/discount/payment/delivery",
        "value": "相关数值",
        "description": "术语说明",
        "position": "位置描述"
      }}
    ],
    "totals_summary": {{
      "subtotal": 小计金额,
      "tax_amount": 税额,
      "discount_amount": 折扣金额,
      "final_total": 最终总额,
      "currency": "货币类型"
    }}
  }},
  "validation": {{
    "calculation_check": true/false,
    "format_consistency": true/false,
    "completeness": "complete/partial/incomplete",
    "data_quality": "high/medium/low"
  }},
  "confidence": 0.95
}}

提取要求：
1. 准确识别所有金额数字，包括小数点
2. 正确识别货币符号和单位
3. 提取数量和对应的物品信息
4. 识别计算关系（单价×数量=小计）
5. 提取所有日期信息
6. 识别财务专业术语
7. 验证计算的正确性
8. 评估数据的完整性和质量

特别注意：
- 中文数字的转换（如：一万 → 10000）
- 金额的千分位分隔符
- 含税/不含税的区分
- 折扣和优惠的计算
- 日期格式的多样性

只返回JSON格式的结果。"""
    
    def _get_content_understanding_template(self) -> str:
        """内容理解模板"""
        return """请全面分析这张图片的内容，并按以下JSON格式返回结构化结果：

{{
  "content_analysis": {{
    "document_info": {{
      "type": "contract/invoice/quotation/report/form",
      "title": "文档标题",
      "language": "chinese/english/mixed",
      "page_number": "页码信息",
      "total_pages": "总页数"
    }},
    "layout_structure": {{
      "header": {{
        "present": true/false,
        "content": "页眉内容",
        "elements": ["logo", "company_name", "contact_info"]
      }},
      "body": {{
        "sections": [
          {{
            "section_type": "table/text/signature/stamp",
            "title": "章节标题",
            "content_summary": "内容摘要",
            "position": "top/middle/bottom"
          }}
        ]
      }},
      "footer": {{
        "present": true/false,
        "content": "页脚内容",
        "elements": ["page_number", "date", "signature"]
      }}
    }},
    "key_information": {{
      "parties": [
        {{
          "role": "buyer/seller/contractor/client",
          "name": "名称",
          "contact": "联系方式",
          "address": "地址"
        }}
      ],
      "subject_matter": "合同/交易标的",
      "key_terms": [
        {{
          "term": "关键条款",
          "value": "条款内容",
          "importance": "high/medium/low"
        }}
      ],
      "important_dates": [
        {{
          "date": "重要日期",
          "description": "日期说明",
          "type": "deadline/effective_date/delivery_date"
        }}
      ]
    }},
    "content_quality": {{
      "text_clarity": "high/medium/low",
      "completeness": "complete/partial/incomplete",
      "legibility": "excellent/good/fair/poor",
      "scan_quality": "high/medium/low"
    }}
  }},
  "extracted_text": {{
    "full_text": "完整文本内容",
    "structured_content": {{
      "headings": ["标题列表"],
      "paragraphs": ["段落内容"],
      "lists": ["列表项目"],
      "tables": ["表格内容摘要"]
    }}
  }},
  "confidence": 0.95
}}

分析要求：
1. 识别文档的类型和用途
2. 分析页面布局和结构
3. 提取关键信息和重要条款
4. 识别参与方信息
5. 提取重要日期和截止时间
6. 评估内容的完整性和质量
7. 结构化组织提取的文本

特别关注：
- 合同条款的完整性
- 金额和数量的准确性
- 日期和时间的正确性
- 签名和印章的存在
- 法律条款的识别

只返回JSON格式的结果。"""
    
    def _get_semantic_comparison_template(self) -> str:
        """语义比对模板"""
        return """请对以下两段内容进行深度语义比对分析，重点关注实质性差异：

内容A：
{content_a}

内容B：
{content_b}

请按以下JSON格式返回详细的比对结果：

{{
  "comparison_analysis": {{
    "overall_assessment": {{
      "semantic_similarity": 0.95,
      "structural_similarity": 0.90,
      "content_equivalence": true/false,
      "substantial_differences": true/false
    }},
    "detailed_differences": [
      {{
        "difference_id": 1,
        "category": "amount/quantity/date/term/clause/structure",
        "type": "addition/deletion/modification/substitution",
        "severity": "critical/major/minor/negligible",
        "description": "差异详细描述",
        "content_a_value": "内容A中的值",
        "content_b_value": "内容B中的值",
        "context": "差异所在的上下文",
        "impact_assessment": {{
          "financial_impact": "high/medium/low/none",
          "legal_impact": "high/medium/low/none",
          "operational_impact": "high/medium/low/none",
          "risk_level": "high/medium/low"
        }},
        "recommendation": "accept/review/reject/clarify"
      }}
    ],
    "identical_elements": [
      {{
        "element_type": "amount/term/clause/date",
        "value": "相同的内容",
        "confidence": 0.98,
        "importance": "high/medium/low"
      }}
    ],
    "financial_consistency": {{
      "total_amounts": {{
        "content_a": "金额A",
        "content_b": "金额B",
        "match": true/false,
        "difference": "差额"
      }},
      "unit_prices": {{
        "consistent": true/false,
        "differences": ["价格差异列表"]
      }},
      "calculations": {{
        "all_correct": true/false,
        "discrepancies": ["计算差异"]
      }},
      "currency_consistency": true/false
    }},
    "structural_analysis": {{
      "format_similarity": 0.95,
      "organization_match": true/false,
      "section_alignment": true/false,
      "completeness_comparison": {{
        "content_a_completeness": 0.95,
        "content_b_completeness": 0.90,
        "missing_in_a": ["A中缺失的内容"],
        "missing_in_b": ["B中缺失的内容"]
      }}
    }},
    "quality_assessment": {{
      "content_a_quality": "high/medium/low",
      "content_b_quality": "high/medium/low",
      "clarity_comparison": "a_better/b_better/equivalent",
      "completeness_comparison": "a_better/b_better/equivalent"
    }}
  }},
  "risk_analysis": {{
    "overall_risk": "low/medium/high/critical",
    "risk_factors": [
      {{
        "factor": "风险因素",
        "description": "风险描述",
        "likelihood": "low/medium/high",
        "impact": "low/medium/high"
      }}
    ],
    "mitigation_suggestions": ["风险缓解建议"]
  }},
  "final_recommendation": {{
    "decision": "approve/approve_with_conditions/reject/require_clarification",
    "confidence": 0.95,
    "reasoning": "决策理由",
    "conditions": ["批准条件（如适用）"],
    "required_actions": ["需要采取的行动"]
  }}
}}

比对要求：
1. 深度分析语义相似性和差异
2. 识别所有实质性变化
3. 评估差异的影响程度
4. 检查财务数据的一致性
5. 分析结构和格式差异
6. 提供风险评估和建议
7. 给出明确的决策建议

特别关注：
- 金额和数量的变化
- 关键条款的修改
- 日期和期限的差异
- 合同条件的变更
- 法律责任的变化

只返回JSON格式的结果。"""
    
    def _get_document_classification_template(self) -> str:
        """文档分类模板"""
        return """请分析这张图片中的文档，并按以下JSON格式返回分类结果：

{{
  "document_classification": {{
    "primary_type": "contract/invoice/quotation/receipt/report/form",
    "sub_type": "具体子类型",
    "confidence": 0.95,
    "classification_features": [
      "分类依据的特征"
    ]
  }},
  "document_attributes": {{
    "business_domain": "construction/manufacturing/services/retail",
    "formality_level": "formal/semi_formal/informal",
    "legal_status": "binding/non_binding/draft",
    "processing_stage": "draft/final/executed/archived"
  }},
  "key_indicators": [
    {{
      "indicator": "关键指标",
      "value": "指标值",
      "confidence": 0.95
    }}
  ]
}}

只返回JSON格式的结果。"""
    
    def _get_quality_assessment_template(self) -> str:
        """质量评估模板"""
        return """请评估这张图片的质量和可读性，并按以下JSON格式返回结果：

{{
  "quality_assessment": {{
    "overall_score": 85,
    "image_quality": {{
      "resolution": "high/medium/low",
      "clarity": "excellent/good/fair/poor",
      "contrast": "high/medium/low",
      "brightness": "optimal/too_bright/too_dark"
    }},
    "text_legibility": {{
      "overall_legibility": "excellent/good/fair/poor",
      "font_clarity": "clear/blurry/distorted",
      "text_size": "adequate/too_small/too_large",
      "handwriting_quality": "clear/unclear/illegible"
    }},
    "scan_quality": {{
      "skew_angle": 0.5,
      "page_completeness": "complete/partial/cropped",
      "noise_level": "low/medium/high",
      "artifacts": ["扫描伪影列表"]
    }},
    "processing_recommendations": [
      "处理建议"
    ]
  }}
}}

只返回JSON格式的结果。"""
    
    def _get_layout_analysis_template(self) -> str:
        """布局分析模板"""
        return """请分析这张图片的页面布局结构，并按以下JSON格式返回结果：

{{
  "layout_analysis": {{
    "page_structure": {{
      "orientation": "portrait/landscape",
      "margins": {{
        "top": 边距值,
        "bottom": 边距值,
        "left": 边距值,
        "right": 边距值
      }},
      "content_areas": [
        {{
          "type": "header/body/footer/sidebar",
          "position": {{
            "x": X坐标,
            "y": Y坐标,
            "width": 宽度,
            "height": 高度
          }},
          "content_type": "text/table/image/signature"
        }}
      ]
    }},
    "text_layout": {{
      "columns": 列数,
      "text_alignment": "left/center/right/justified",
      "line_spacing": "single/double/custom",
      "paragraph_spacing": "normal/tight/loose"
    }},
    "visual_elements": [
      {{
        "type": "logo/signature/stamp/image",
        "position": "位置描述",
        "size": "大小描述"
      }}
    ]
  }}
}}

只返回JSON格式的结果。"""
    
    def get_available_templates(self) -> List[str]:
        """获取可用的模板列表"""
        return list(self.templates.keys())
    
    def add_custom_template(self, name: str, template: str) -> bool:
        """添加自定义模板"""
        try:
            self.templates[name] = template
            logger.info(f"添加自定义模板: {name}")
            return True
        except Exception as e:
            logger.error(f"添加自定义模板失败: {e}")
            return False
