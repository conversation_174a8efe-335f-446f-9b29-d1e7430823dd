"""
错误处理中间件
"""
import traceback
from typing import Callable

from fastapi import Request, Response, status
from fastapi.responses import JSONResponse
from loguru import logger
from starlette.middleware.base import BaseHTTPMiddleware


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """全局错误处理中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
            
        except ValueError as e:
            logger.warning(f"参数错误: {e}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "error": "参数错误",
                    "message": str(e),
                    "code": "INVALID_PARAMETER"
                }
            )
            
        except FileNotFoundError as e:
            logger.warning(f"文件未找到: {e}")
            return JSONResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                content={
                    "error": "文件未找到",
                    "message": str(e),
                    "code": "FILE_NOT_FOUND"
                }
            )
            
        except PermissionError as e:
            logger.warning(f"权限错误: {e}")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "error": "权限不足",
                    "message": str(e),
                    "code": "PERMISSION_DENIED"
                }
            )
            
        except Exception as e:
            # 记录详细错误信息
            error_traceback = traceback.format_exc()
            logger.error(f"未处理的异常: {e}\n{error_traceback}")
            
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "error": "服务器内部错误",
                    "message": "系统发生未知错误，请稍后重试",
                    "code": "INTERNAL_SERVER_ERROR"
                }
            )
