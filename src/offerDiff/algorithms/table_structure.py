"""
表格结构分析算法
"""
from typing import List, Dict, Tuple, Optional
import numpy as np
import cv2
from loguru import logger


class TableStructureAnalyzer:
    """表格结构分析器"""
    
    def __init__(self):
        self.min_line_length = 50  # 最小线条长度
        self.line_gap_threshold = 10  # 线条间隙阈值
        self.cell_merge_threshold = 0.8  # 单元格合并阈值
    
    def analyze_structure(self, image: np.ndarray, table_bbox: Dict) -> Dict:
        """
        分析表格结构
        
        Args:
            image: 输入图像
            table_bbox: 表格边界框
            
        Returns:
            表格结构分析结果
        """
        try:
            # 裁剪表格区域
            x1, y1, x2, y2 = table_bbox["x1"], table_bbox["y1"], table_bbox["x2"], table_bbox["y2"]
            table_region = image[y1:y2, x1:x2]
            
            # 转换为灰度图像
            if len(table_region.shape) == 3:
                gray = cv2.cvtColor(table_region, cv2.COLOR_RGB2GRAY)
            else:
                gray = table_region
            
            # 检测线条
            horizontal_lines = self._detect_horizontal_lines(gray)
            vertical_lines = self._detect_vertical_lines(gray)
            
            # 分析网格结构
            grid_structure = self._analyze_grid_structure(horizontal_lines, vertical_lines, gray.shape)
            
            # 检测表头
            header_info = self._detect_table_header(gray, grid_structure)
            
            # 检测合并单元格
            merged_cells = self._detect_merged_cells_structure(gray, grid_structure)
            
            # 分析表格类型
            table_type = self._classify_table_type(grid_structure, header_info, merged_cells)
            
            structure = {
                "horizontal_lines": horizontal_lines,
                "vertical_lines": vertical_lines,
                "grid": grid_structure,
                "header": header_info,
                "merged_cells": merged_cells,
                "table_type": table_type,
                "complexity_score": self._calculate_complexity_score(grid_structure, merged_cells)
            }
            
            logger.info(f"表格结构分析完成: {grid_structure['rows']}行 x {grid_structure['cols']}列")
            return structure
            
        except Exception as e:
            logger.error(f"表格结构分析失败: {e}")
            return {"error": str(e)}
    
    def _detect_horizontal_lines(self, gray_image: np.ndarray) -> List[Dict]:
        """检测水平线条"""
        try:
            # 创建水平线检测核
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            
            # 二值化
            _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 形态学操作检测水平线
            horizontal_lines_img = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
            
            # 使用霍夫变换检测直线
            lines = cv2.HoughLinesP(
                horizontal_lines_img, 
                1, 
                np.pi/180, 
                threshold=50,
                minLineLength=self.min_line_length,
                maxLineGap=self.line_gap_threshold
            )
            
            horizontal_lines = []
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # 过滤水平线（角度接近0度）
                    if abs(y2 - y1) <= 5:  # 允许小幅度倾斜
                        line_info = {
                            "start": (x1, y1),
                            "end": (x2, y2),
                            "length": np.sqrt((x2-x1)**2 + (y2-y1)**2),
                            "y_position": (y1 + y2) // 2,
                            "x_start": min(x1, x2),
                            "x_end": max(x1, x2)
                        }
                        horizontal_lines.append(line_info)
            
            # 合并相近的水平线
            horizontal_lines = self._merge_similar_lines(horizontal_lines, "horizontal")
            
            return horizontal_lines
            
        except Exception as e:
            logger.error(f"检测水平线失败: {e}")
            return []
    
    def _detect_vertical_lines(self, gray_image: np.ndarray) -> List[Dict]:
        """检测垂直线条"""
        try:
            # 创建垂直线检测核
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
            
            # 二值化
            _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 形态学操作检测垂直线
            vertical_lines_img = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
            
            # 使用霍夫变换检测直线
            lines = cv2.HoughLinesP(
                vertical_lines_img, 
                1, 
                np.pi/180, 
                threshold=50,
                minLineLength=self.min_line_length,
                maxLineGap=self.line_gap_threshold
            )
            
            vertical_lines = []
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # 过滤垂直线（角度接近90度）
                    if abs(x2 - x1) <= 5:  # 允许小幅度倾斜
                        line_info = {
                            "start": (x1, y1),
                            "end": (x2, y2),
                            "length": np.sqrt((x2-x1)**2 + (y2-y1)**2),
                            "x_position": (x1 + x2) // 2,
                            "y_start": min(y1, y2),
                            "y_end": max(y1, y2)
                        }
                        vertical_lines.append(line_info)
            
            # 合并相近的垂直线
            vertical_lines = self._merge_similar_lines(vertical_lines, "vertical")
            
            return vertical_lines
            
        except Exception as e:
            logger.error(f"检测垂直线失败: {e}")
            return []
    
    def _merge_similar_lines(self, lines: List[Dict], line_type: str) -> List[Dict]:
        """合并相似的线条"""
        if not lines:
            return lines
        
        try:
            merged_lines = []
            used = set()
            
            for i, line1 in enumerate(lines):
                if i in used:
                    continue
                
                similar_lines = [line1]
                used.add(i)
                
                for j, line2 in enumerate(lines[i+1:], i+1):
                    if j in used:
                        continue
                    
                    # 检查是否为相似线条
                    if line_type == "horizontal":
                        position_diff = abs(line1["y_position"] - line2["y_position"])
                        if position_diff <= 5:  # Y坐标相近
                            similar_lines.append(line2)
                            used.add(j)
                    else:  # vertical
                        position_diff = abs(line1["x_position"] - line2["x_position"])
                        if position_diff <= 5:  # X坐标相近
                            similar_lines.append(line2)
                            used.add(j)
                
                # 合并相似线条
                if len(similar_lines) > 1:
                    merged_line = self._merge_line_group(similar_lines, line_type)
                    merged_lines.append(merged_line)
                else:
                    merged_lines.append(line1)
            
            return merged_lines
            
        except Exception as e:
            logger.error(f"合并线条失败: {e}")
            return lines
    
    def _merge_line_group(self, lines: List[Dict], line_type: str) -> Dict:
        """合并一组线条"""
        if line_type == "horizontal":
            # 计算平均Y坐标
            avg_y = sum(line["y_position"] for line in lines) // len(lines)
            min_x = min(line["x_start"] for line in lines)
            max_x = max(line["x_end"] for line in lines)
            
            return {
                "start": (min_x, avg_y),
                "end": (max_x, avg_y),
                "length": max_x - min_x,
                "y_position": avg_y,
                "x_start": min_x,
                "x_end": max_x
            }
        else:  # vertical
            # 计算平均X坐标
            avg_x = sum(line["x_position"] for line in lines) // len(lines)
            min_y = min(line["y_start"] for line in lines)
            max_y = max(line["y_end"] for line in lines)
            
            return {
                "start": (avg_x, min_y),
                "end": (avg_x, max_y),
                "length": max_y - min_y,
                "x_position": avg_x,
                "y_start": min_y,
                "y_end": max_y
            }
    
    def _analyze_grid_structure(self, horizontal_lines: List[Dict], vertical_lines: List[Dict], image_shape: Tuple) -> Dict:
        """分析网格结构"""
        try:
            height, width = image_shape
            
            # 获取行分隔位置
            row_positions = [0]  # 添加顶部边界
            for line in horizontal_lines:
                row_positions.append(line["y_position"])
            row_positions.append(height)  # 添加底部边界
            row_positions = sorted(list(set(row_positions)))
            
            # 获取列分隔位置
            col_positions = [0]  # 添加左边界
            for line in vertical_lines:
                col_positions.append(line["x_position"])
            col_positions.append(width)  # 添加右边界
            col_positions = sorted(list(set(col_positions)))
            
            # 计算行和列数
            rows = len(row_positions) - 1
            cols = len(col_positions) - 1
            
            # 生成单元格网格
            cells = []
            for i in range(rows):
                row_cells = []
                for j in range(cols):
                    cell = {
                        "row": i,
                        "col": j,
                        "x1": col_positions[j],
                        "y1": row_positions[i],
                        "x2": col_positions[j + 1],
                        "y2": row_positions[i + 1],
                        "width": col_positions[j + 1] - col_positions[j],
                        "height": row_positions[i + 1] - row_positions[i]
                    }
                    row_cells.append(cell)
                cells.append(row_cells)
            
            grid_structure = {
                "rows": rows,
                "cols": cols,
                "row_positions": row_positions,
                "col_positions": col_positions,
                "cells": cells,
                "total_cells": rows * cols
            }
            
            return grid_structure
            
        except Exception as e:
            logger.error(f"分析网格结构失败: {e}")
            return {"rows": 0, "cols": 0, "cells": []}
    
    def _detect_table_header(self, gray_image: np.ndarray, grid_structure: Dict) -> Dict:
        """检测表头"""
        try:
            if grid_structure["rows"] < 2:
                return {"has_header": False}
            
            cells = grid_structure["cells"]
            first_row = cells[0]
            
            # 分析第一行的特征
            header_features = []
            
            for cell in first_row:
                # 提取单元格区域
                cell_region = gray_image[cell["y1"]:cell["y2"], cell["x1"]:cell["x2"]]
                
                if cell_region.size > 0:
                    # 计算特征
                    mean_intensity = np.mean(cell_region)
                    std_intensity = np.std(cell_region)
                    
                    # 检测是否有粗体文字（更暗的区域）
                    dark_ratio = np.sum(cell_region < mean_intensity * 0.8) / cell_region.size
                    
                    header_features.append({
                        "mean_intensity": mean_intensity,
                        "std_intensity": std_intensity,
                        "dark_ratio": dark_ratio
                    })
            
            # 判断是否为表头
            if header_features:
                avg_dark_ratio = np.mean([f["dark_ratio"] for f in header_features])
                has_header = avg_dark_ratio > 0.3  # 如果暗色区域比例高，可能是表头
            else:
                has_header = False
            
            return {
                "has_header": has_header,
                "header_row": 0 if has_header else None,
                "header_features": header_features
            }
            
        except Exception as e:
            logger.error(f"检测表头失败: {e}")
            return {"has_header": False}
    
    def _detect_merged_cells_structure(self, gray_image: np.ndarray, grid_structure: Dict) -> List[Dict]:
        """检测合并单元格结构"""
        try:
            merged_cells = []
            cells = grid_structure["cells"]
            
            if not cells:
                return merged_cells
            
            # 检查每个单元格是否与相邻单元格合并
            for i, row in enumerate(cells):
                for j, cell in enumerate(row):
                    # 检查右侧合并
                    if j < len(row) - 1:
                        right_cell = row[j + 1]
                        if self._cells_are_merged(gray_image, cell, right_cell, "horizontal"):
                            merged_cells.append({
                                "type": "horizontal",
                                "start_row": i,
                                "start_col": j,
                                "end_row": i,
                                "end_col": j + 1,
                                "cells": [cell, right_cell]
                            })
                    
                    # 检查下方合并
                    if i < len(cells) - 1:
                        bottom_cell = cells[i + 1][j]
                        if self._cells_are_merged(gray_image, cell, bottom_cell, "vertical"):
                            merged_cells.append({
                                "type": "vertical",
                                "start_row": i,
                                "start_col": j,
                                "end_row": i + 1,
                                "end_col": j,
                                "cells": [cell, bottom_cell]
                            })
            
            return merged_cells
            
        except Exception as e:
            logger.error(f"检测合并单元格结构失败: {e}")
            return []
    
    def _cells_are_merged(self, gray_image: np.ndarray, cell1: Dict, cell2: Dict, direction: str) -> bool:
        """判断两个单元格是否合并"""
        try:
            if direction == "horizontal":
                # 检查两个单元格之间的垂直分隔线
                x = cell1["x2"]
                y1 = max(cell1["y1"], cell2["y1"])
                y2 = min(cell1["y2"], cell2["y2"])
                
                if y2 > y1 and x < gray_image.shape[1]:
                    # 提取分隔线区域
                    separator = gray_image[y1:y2, max(0, x-2):min(gray_image.shape[1], x+3)]
                    
                    if separator.size > 0:
                        # 检测是否有明显的分隔线
                        edges = cv2.Canny(separator, 50, 150)
                        edge_density = np.sum(edges > 0) / edges.size
                        return edge_density < 0.1  # 低边缘密度表示可能合并
            
            else:  # vertical
                # 检查两个单元格之间的水平分隔线
                y = cell1["y2"]
                x1 = max(cell1["x1"], cell2["x1"])
                x2 = min(cell1["x2"], cell2["x2"])
                
                if x2 > x1 and y < gray_image.shape[0]:
                    # 提取分隔线区域
                    separator = gray_image[max(0, y-2):min(gray_image.shape[0], y+3), x1:x2]
                    
                    if separator.size > 0:
                        # 检测是否有明显的分隔线
                        edges = cv2.Canny(separator, 50, 150)
                        edge_density = np.sum(edges > 0) / edges.size
                        return edge_density < 0.1
            
            return False
            
        except Exception as e:
            logger.error(f"判断单元格合并失败: {e}")
            return False
    
    def _classify_table_type(self, grid_structure: Dict, header_info: Dict, merged_cells: List[Dict]) -> str:
        """分类表格类型"""
        try:
            rows = grid_structure["rows"]
            cols = grid_structure["cols"]
            has_header = header_info["has_header"]
            has_merged = len(merged_cells) > 0
            
            # 简单分类逻辑
            if rows <= 2 and cols <= 3:
                return "simple"
            elif has_merged and (rows > 5 or cols > 5):
                return "complex"
            elif has_header and rows > 3:
                return "standard"
            elif rows > 10 or cols > 8:
                return "large"
            else:
                return "medium"
                
        except Exception as e:
            logger.error(f"分类表格类型失败: {e}")
            return "unknown"
    
    def _calculate_complexity_score(self, grid_structure: Dict, merged_cells: List[Dict]) -> float:
        """计算表格复杂度评分"""
        try:
            rows = grid_structure["rows"]
            cols = grid_structure["cols"]
            total_cells = rows * cols
            merged_count = len(merged_cells)
            
            # 基础复杂度（基于尺寸）
            size_complexity = min(1.0, (rows * cols) / 100)
            
            # 合并单元格复杂度
            merge_complexity = min(1.0, merged_count / total_cells) if total_cells > 0 else 0
            
            # 综合复杂度评分
            complexity_score = (size_complexity * 0.6 + merge_complexity * 0.4)
            
            return complexity_score
            
        except Exception as e:
            logger.error(f"计算复杂度评分失败: {e}")
            return 0.0
