"""
噪声减少算法
"""
from typing import Tuple, Optional
import numpy as np
import cv2
from scipy import ndimage
from loguru import logger


def gaussian_noise_reduction(image: np.ndarray, kernel_size: int = 5, sigma: float = 1.0) -> np.ndarray:
    """
    高斯噪声减少
    
    Args:
        image: 输入图像
        kernel_size: 核大小
        sigma: 标准差
        
    Returns:
        去噪后的图像
    """
    try:
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
    except Exception as e:
        logger.error(f"高斯去噪失败: {e}")
        return image


def median_noise_reduction(image: np.ndarray, kernel_size: int = 5) -> np.ndarray:
    """
    中值滤波去噪（对椒盐噪声效果好）
    
    Args:
        image: 输入图像
        kernel_size: 核大小
        
    Returns:
        去噪后的图像
    """
    try:
        return cv2.medianBlur(image, kernel_size)
    except Exception as e:
        logger.error(f"中值滤波去噪失败: {e}")
        return image


def bilateral_noise_reduction(image: np.ndarray, d: int = 9, sigma_color: float = 75, sigma_space: float = 75) -> np.ndarray:
    """
    双边滤波去噪（保持边缘）
    
    Args:
        image: 输入图像
        d: 邻域直径
        sigma_color: 颜色空间标准差
        sigma_space: 坐标空间标准差
        
    Returns:
        去噪后的图像
    """
    try:
        return cv2.bilateralFilter(image, d, sigma_color, sigma_space)
    except Exception as e:
        logger.error(f"双边滤波去噪失败: {e}")
        return image


def non_local_means_denoising(image: np.ndarray, h: float = 10, template_window_size: int = 7, search_window_size: int = 21) -> np.ndarray:
    """
    非局部均值去噪
    
    Args:
        image: 输入图像
        h: 滤波强度
        template_window_size: 模板窗口大小
        search_window_size: 搜索窗口大小
        
    Returns:
        去噪后的图像
    """
    try:
        if len(image.shape) == 3:
            return cv2.fastNlMeansDenoisingColored(image, None, h, h, template_window_size, search_window_size)
        else:
            return cv2.fastNlMeansDenoising(image, None, h, template_window_size, search_window_size)
    except Exception as e:
        logger.error(f"非局部均值去噪失败: {e}")
        return image


def morphological_noise_reduction(image: np.ndarray, operation: str = "opening", kernel_size: int = 3) -> np.ndarray:
    """
    形态学去噪
    
    Args:
        image: 输入图像
        operation: 形态学操作 ("opening", "closing", "gradient")
        kernel_size: 核大小
        
    Returns:
        去噪后的图像
    """
    try:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        
        if operation == "opening":
            return cv2.morphologyEx(image, cv2.MORPH_OPEN, kernel)
        elif operation == "closing":
            return cv2.morphologyEx(image, cv2.MORPH_CLOSE, kernel)
        elif operation == "gradient":
            return cv2.morphologyEx(image, cv2.MORPH_GRADIENT, kernel)
        else:
            logger.warning(f"未知的形态学操作: {operation}")
            return image
            
    except Exception as e:
        logger.error(f"形态学去噪失败: {e}")
        return image


def wiener_filter_denoising(image: np.ndarray, noise_variance: Optional[float] = None) -> np.ndarray:
    """
    维纳滤波去噪
    
    Args:
        image: 输入图像
        noise_variance: 噪声方差
        
    Returns:
        去噪后的图像
    """
    try:
        # 转换为浮点数
        image_float = image.astype(np.float32)
        
        # 估计噪声方差
        if noise_variance is None:
            # 使用拉普拉斯算子估计噪声
            laplacian = cv2.Laplacian(image_float, cv2.CV_32F)
            noise_variance = np.var(laplacian) * 0.5
        
        # FFT变换
        f_transform = np.fft.fft2(image_float)
        f_shift = np.fft.fftshift(f_transform)
        
        # 计算功率谱
        power_spectrum = np.abs(f_shift) ** 2
        
        # 维纳滤波
        wiener_filter = power_spectrum / (power_spectrum + noise_variance)
        
        # 应用滤波器
        filtered_f = f_shift * wiener_filter
        
        # 逆FFT变换
        f_ishift = np.fft.ifftshift(filtered_f)
        filtered_image = np.fft.ifft2(f_ishift)
        filtered_image = np.real(filtered_image)
        
        # 转换回uint8
        filtered_image = np.clip(filtered_image, 0, 255).astype(np.uint8)
        
        return filtered_image
        
    except Exception as e:
        logger.error(f"维纳滤波去噪失败: {e}")
        return image


def adaptive_noise_reduction(image: np.ndarray, noise_type: str = "auto") -> np.ndarray:
    """
    自适应噪声减少
    
    Args:
        image: 输入图像
        noise_type: 噪声类型 ("auto", "gaussian", "salt_pepper", "speckle")
        
    Returns:
        去噪后的图像
    """
    try:
        if noise_type == "auto":
            # 自动检测噪声类型
            noise_type = detect_noise_type(image)
        
        if noise_type == "gaussian":
            # 高斯噪声：使用双边滤波
            return bilateral_noise_reduction(image)
            
        elif noise_type == "salt_pepper":
            # 椒盐噪声：使用中值滤波
            return median_noise_reduction(image)
            
        elif noise_type == "speckle":
            # 斑点噪声：使用非局部均值
            return non_local_means_denoising(image)
            
        else:
            # 默认使用双边滤波
            return bilateral_noise_reduction(image)
            
    except Exception as e:
        logger.error(f"自适应去噪失败: {e}")
        return image


def detect_noise_type(image: np.ndarray) -> str:
    """
    检测噪声类型
    
    Args:
        image: 输入图像
        
    Returns:
        噪声类型
    """
    try:
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image
        
        # 计算图像统计信息
        mean_val = np.mean(gray)
        std_val = np.std(gray)
        
        # 检测椒盐噪声
        # 计算极值像素比例
        black_pixels = np.sum(gray == 0)
        white_pixels = np.sum(gray == 255)
        total_pixels = gray.size
        extreme_ratio = (black_pixels + white_pixels) / total_pixels
        
        if extreme_ratio > 0.01:  # 1%以上的极值像素
            return "salt_pepper"
        
        # 检测高斯噪声
        # 使用拉普拉斯算子检测噪声
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        noise_variance = np.var(laplacian)
        
        if noise_variance > 1000:
            return "gaussian"
        
        # 检测斑点噪声
        # 计算局部方差
        kernel = np.ones((5, 5), np.float32) / 25
        local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
        local_variance = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
        avg_local_variance = np.mean(local_variance)
        
        if avg_local_variance > 500:
            return "speckle"
        
        # 默认返回高斯噪声
        return "gaussian"
        
    except Exception as e:
        logger.error(f"噪声类型检测失败: {e}")
        return "gaussian"


def edge_preserving_smoothing(image: np.ndarray, flags: int = 1, sigma_s: float = 50, sigma_r: float = 0.4) -> np.ndarray:
    """
    边缘保持平滑
    
    Args:
        image: 输入图像
        flags: 算法标志
        sigma_s: 邻域大小
        sigma_r: 边缘保持程度
        
    Returns:
        平滑后的图像
    """
    try:
        return cv2.edgePreservingFilter(image, flags=flags, sigma_s=sigma_s, sigma_r=sigma_r)
    except Exception as e:
        logger.error(f"边缘保持平滑失败: {e}")
        return image


def detail_enhance_denoising(image: np.ndarray, sigma_s: float = 10, sigma_r: float = 0.15) -> np.ndarray:
    """
    细节增强去噪
    
    Args:
        image: 输入图像
        sigma_s: 邻域大小
        sigma_r: 边缘保持程度
        
    Returns:
        增强后的图像
    """
    try:
        return cv2.detailEnhance(image, sigma_s=sigma_s, sigma_r=sigma_r)
    except Exception as e:
        logger.error(f"细节增强去噪失败: {e}")
        return image
