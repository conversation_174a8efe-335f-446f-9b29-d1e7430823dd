"""
倾斜校正算法
"""
import math
from typing import Tuple, Optional, List
import numpy as np
import cv2
from scipy import ndimage
from loguru import logger


def detect_skew_angle_hough(image: np.ndarray, angle_range: Tuple[float, float] = (-10, 10)) -> float:
    """
    使用霍夫变换检测倾斜角度
    
    Args:
        image: 输入图像
        angle_range: 角度检测范围
        
    Returns:
        倾斜角度（度）
    """
    try:
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # 霍夫变换检测直线
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is None:
            return 0.0
        
        # 收集角度
        angles = []
        min_angle, max_angle = angle_range
        
        for rho, theta in lines:
            angle = theta * 180 / np.pi
            # 转换为相对于水平线的角度
            if angle > 90:
                angle = angle - 180
            
            # 过滤角度范围
            if min_angle <= angle <= max_angle:
                angles.append(angle)
        
        if not angles:
            return 0.0
        
        # 使用中位数作为最终角度
        return np.median(angles)
        
    except Exception as e:
        logger.error(f"霍夫变换倾斜检测失败: {e}")
        return 0.0


def detect_skew_angle_projection(image: np.ndarray, angle_range: Tuple[float, float] = (-10, 10), step: float = 0.1) -> float:
    """
    使用投影法检测倾斜角度
    
    Args:
        image: 输入图像
        angle_range: 角度检测范围
        step: 角度步长
        
    Returns:
        倾斜角度（度）
    """
    try:
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 反转（文字为白色，背景为黑色）
        binary = 255 - binary
        
        min_angle, max_angle = angle_range
        best_angle = 0
        max_variance = 0
        
        # 遍历角度范围
        angle = min_angle
        while angle <= max_angle:
            # 旋转图像
            rotated = rotate_image(binary, angle)
            
            # 计算水平投影
            projection = np.sum(rotated, axis=1)
            
            # 计算投影方差（方差越大，说明文本行越清晰）
            variance = np.var(projection)
            
            if variance > max_variance:
                max_variance = variance
                best_angle = angle
            
            angle += step
        
        return best_angle
        
    except Exception as e:
        logger.error(f"投影法倾斜检测失败: {e}")
        return 0.0


def detect_skew_angle_radon(image: np.ndarray, angle_range: Tuple[float, float] = (-10, 10)) -> float:
    """
    使用Radon变换检测倾斜角度
    
    Args:
        image: 输入图像
        angle_range: 角度检测范围
        
    Returns:
        倾斜角度（度）
    """
    try:
        from skimage.transform import radon
        
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # 生成角度数组
        min_angle, max_angle = angle_range
        angles = np.arange(min_angle, max_angle + 0.1, 0.1)
        
        # Radon变换
        sinogram = radon(binary, theta=angles)
        
        # 找到最大投影对应的角度
        projection_sums = np.sum(sinogram, axis=0)
        best_angle_idx = np.argmax(projection_sums)
        best_angle = angles[best_angle_idx]
        
        return best_angle
        
    except ImportError:
        logger.warning("scikit-image未安装，使用霍夫变换方法")
        return detect_skew_angle_hough(image, angle_range)
    except Exception as e:
        logger.error(f"Radon变换倾斜检测失败: {e}")
        return 0.0


def rotate_image(image: np.ndarray, angle: float, background_color: int = 255) -> np.ndarray:
    """
    旋转图像
    
    Args:
        image: 输入图像
        angle: 旋转角度（度）
        background_color: 背景颜色
        
    Returns:
        旋转后的图像
    """
    try:
        height, width = image.shape[:2]
        center = (width // 2, height // 2)
        
        # 创建旋转矩阵
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        
        # 计算新的边界尺寸
        cos_angle = abs(rotation_matrix[0, 0])
        sin_angle = abs(rotation_matrix[0, 1])
        new_width = int((height * sin_angle) + (width * cos_angle))
        new_height = int((height * cos_angle) + (width * sin_angle))
        
        # 调整旋转中心
        rotation_matrix[0, 2] += (new_width / 2) - center[0]
        rotation_matrix[1, 2] += (new_height / 2) - center[1]
        
        # 执行旋转
        rotated = cv2.warpAffine(
            image, 
            rotation_matrix, 
            (new_width, new_height),
            flags=cv2.INTER_CUBIC,
            borderMode=cv2.BORDER_CONSTANT,
            borderValue=background_color
        )
        
        return rotated
        
    except Exception as e:
        logger.error(f"图像旋转失败: {e}")
        return image


def correct_skew_auto(image: np.ndarray, method: str = "hough", max_angle: float = 10.0) -> Tuple[np.ndarray, float]:
    """
    自动倾斜校正
    
    Args:
        image: 输入图像
        method: 检测方法 ("hough", "projection", "radon")
        max_angle: 最大校正角度
        
    Returns:
        (校正后的图像, 检测到的角度)
    """
    try:
        angle_range = (-max_angle, max_angle)
        
        # 根据方法检测角度
        if method == "hough":
            skew_angle = detect_skew_angle_hough(image, angle_range)
        elif method == "projection":
            skew_angle = detect_skew_angle_projection(image, angle_range)
        elif method == "radon":
            skew_angle = detect_skew_angle_radon(image, angle_range)
        else:
            logger.warning(f"未知的检测方法: {method}，使用霍夫变换")
            skew_angle = detect_skew_angle_hough(image, angle_range)
        
        # 如果角度太小，不进行校正
        if abs(skew_angle) < 0.5:
            return image, skew_angle
        
        # 校正倾斜
        corrected_image = rotate_image(image, skew_angle)
        
        logger.info(f"倾斜校正完成: {skew_angle:.2f}°")
        return corrected_image, skew_angle
        
    except Exception as e:
        logger.error(f"自动倾斜校正失败: {e}")
        return image, 0.0


def detect_text_lines(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """
    检测文本行
    
    Args:
        image: 输入图像
        
    Returns:
        文本行边界框列表 [(x, y, w, h), ...]
    """
    try:
        # 转换为灰度图像
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image.copy()
        
        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        
        # 水平形态学操作连接文字
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
        connected = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # 查找轮廓
        contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 过滤和排序文本行
        text_lines = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            
            # 过滤条件：面积、宽高比
            area = w * h
            aspect_ratio = w / h if h > 0 else 0
            
            if area > 500 and aspect_ratio > 3:  # 文本行通常比较长
                text_lines.append((x, y, w, h))
        
        # 按y坐标排序
        text_lines.sort(key=lambda x: x[1])
        
        return text_lines
        
    except Exception as e:
        logger.error(f"文本行检测失败: {e}")
        return []


def calculate_line_angles(text_lines: List[Tuple[int, int, int, int]]) -> List[float]:
    """
    计算文本行角度
    
    Args:
        text_lines: 文本行边界框列表
        
    Returns:
        角度列表
    """
    try:
        angles = []
        
        for i in range(len(text_lines) - 1):
            x1, y1, w1, h1 = text_lines[i]
            x2, y2, w2, h2 = text_lines[i + 1]
            
            # 计算两个文本行中心点的连线角度
            center1_x = x1 + w1 // 2
            center1_y = y1 + h1 // 2
            center2_x = x2 + w2 // 2
            center2_y = y2 + h2 // 2
            
            # 计算角度
            if center2_x != center1_x:
                angle = math.atan2(center2_y - center1_y, center2_x - center1_x)
                angle_degrees = math.degrees(angle)
                angles.append(angle_degrees)
        
        return angles
        
    except Exception as e:
        logger.error(f"计算文本行角度失败: {e}")
        return []


def robust_skew_detection(image: np.ndarray) -> float:
    """
    鲁棒的倾斜检测（结合多种方法）
    
    Args:
        image: 输入图像
        
    Returns:
        倾斜角度
    """
    try:
        angles = []
        
        # 方法1：霍夫变换
        try:
            angle1 = detect_skew_angle_hough(image)
            if abs(angle1) <= 10:
                angles.append(angle1)
        except:
            pass
        
        # 方法2：投影法
        try:
            angle2 = detect_skew_angle_projection(image)
            if abs(angle2) <= 10:
                angles.append(angle2)
        except:
            pass
        
        # 方法3：文本行分析
        try:
            text_lines = detect_text_lines(image)
            if len(text_lines) >= 3:
                line_angles = calculate_line_angles(text_lines)
                if line_angles:
                    angle3 = np.median(line_angles)
                    if abs(angle3) <= 10:
                        angles.append(angle3)
        except:
            pass
        
        # 综合判断
        if not angles:
            return 0.0
        
        # 使用中位数作为最终结果
        final_angle = np.median(angles)
        
        logger.info(f"鲁棒倾斜检测结果: {final_angle:.2f}° (基于{len(angles)}种方法)")
        return final_angle
        
    except Exception as e:
        logger.error(f"鲁棒倾斜检测失败: {e}")
        return 0.0
