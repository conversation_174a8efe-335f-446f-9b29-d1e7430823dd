"""
PDF转图像处理器
"""
import os
from pathlib import Path
from typing import List, Tuple, Optional
import tempfile

from pdf2image import convert_from_path
from PIL import Image
from loguru import logger

from config.settings import settings


class PDFConverter:
    """PDF转图像转换器"""
    
    def __init__(self):
        self.dpi = 300  # 标准DPI
        self.format = "PNG"  # 输出格式
        self.quality = 95  # 图像质量
    
    def convert_pdf_to_images(
        self, 
        pdf_path: str, 
        output_dir: Optional[str] = None,
        dpi: Optional[int] = None
    ) -> List[str]:
        """
        将PDF转换为图像文件
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录，如果为None则使用临时目录
            dpi: 分辨率，如果为None则使用默认值
            
        Returns:
            图像文件路径列表
        """
        try:
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"PDF文件不存在: {pdf_path}")
            
            # 设置参数
            dpi = dpi or self.dpi
            output_dir = output_dir or tempfile.mkdtemp()
            
            # 确保输出目录存在
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            logger.info(f"开始转换PDF: {pdf_path}, DPI: {dpi}")
            
            # 转换PDF为图像
            images = convert_from_path(
                pdf_path,
                dpi=dpi,
                output_folder=output_dir,
                fmt=self.format.lower(),
                thread_count=2,  # 使用多线程加速
                use_pdftocairo=True,  # 使用更好的渲染引擎
                grayscale=False,  # 保持彩色
                transparent=False  # 不使用透明背景
            )
            
            # 保存图像文件
            image_paths = []
            pdf_name = Path(pdf_path).stem
            
            for i, image in enumerate(images):
                image_path = os.path.join(output_dir, f"{pdf_name}_page_{i+1:03d}.{self.format.lower()}")
                
                # 优化图像质量
                if self.format.upper() == "JPEG":
                    image.save(image_path, self.format, quality=self.quality, optimize=True)
                else:
                    image.save(image_path, self.format, optimize=True)
                
                image_paths.append(image_path)
                logger.debug(f"保存页面 {i+1}: {image_path}")
            
            logger.info(f"PDF转换完成，共 {len(image_paths)} 页")
            return image_paths
            
        except Exception as e:
            logger.error(f"PDF转换失败: {e}")
            raise
    
    def get_pdf_info(self, pdf_path: str) -> dict:
        """
        获取PDF基本信息
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            PDF信息字典
        """
        try:
            from PyPDF2 import PdfReader
            
            with open(pdf_path, 'rb') as file:
                reader = PdfReader(file)
                
                info = {
                    "page_count": len(reader.pages),
                    "file_size": os.path.getsize(pdf_path),
                    "encrypted": reader.is_encrypted,
                    "metadata": {}
                }
                
                # 获取元数据
                if reader.metadata:
                    info["metadata"] = {
                        "title": reader.metadata.get("/Title", ""),
                        "author": reader.metadata.get("/Author", ""),
                        "creator": reader.metadata.get("/Creator", ""),
                        "producer": reader.metadata.get("/Producer", ""),
                        "creation_date": reader.metadata.get("/CreationDate", ""),
                        "modification_date": reader.metadata.get("/ModDate", "")
                    }
                
                # 获取第一页尺寸
                if len(reader.pages) > 0:
                    first_page = reader.pages[0]
                    mediabox = first_page.mediabox
                    info["page_size"] = {
                        "width": float(mediabox.width),
                        "height": float(mediabox.height)
                    }
                
                return info
                
        except Exception as e:
            logger.error(f"获取PDF信息失败: {e}")
            return {"error": str(e)}
    
    def validate_pdf(self, pdf_path: str) -> Tuple[bool, str]:
        """
        验证PDF文件
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not os.path.exists(pdf_path):
                return False, "文件不存在"
            
            # 检查文件大小
            file_size = os.path.getsize(pdf_path)
            if file_size == 0:
                return False, "文件为空"
            
            if file_size > settings.max_file_size_bytes:
                return False, f"文件过大，超过限制 {settings.max_file_size}"
            
            # 检查文件格式
            with open(pdf_path, 'rb') as file:
                header = file.read(4)
                if header != b'%PDF':
                    return False, "不是有效的PDF文件"
            
            # 尝试读取PDF
            info = self.get_pdf_info(pdf_path)
            if "error" in info:
                return False, f"PDF文件损坏: {info['error']}"
            
            # 检查页数
            if info.get("page_count", 0) == 0:
                return False, "PDF文件没有页面"
            
            if info.get("page_count", 0) > 100:  # 限制最大页数
                return False, "PDF页数过多，超过100页限制"
            
            return True, "验证通过"
            
        except Exception as e:
            return False, f"验证失败: {e}"
    
    def batch_convert(self, pdf_paths: List[str], output_base_dir: str) -> dict:
        """
        批量转换PDF文件
        
        Args:
            pdf_paths: PDF文件路径列表
            output_base_dir: 输出基础目录
            
        Returns:
            转换结果字典
        """
        results = {
            "success": [],
            "failed": [],
            "total": len(pdf_paths)
        }
        
        for pdf_path in pdf_paths:
            try:
                # 为每个PDF创建单独的输出目录
                pdf_name = Path(pdf_path).stem
                output_dir = os.path.join(output_base_dir, pdf_name)
                
                # 转换PDF
                image_paths = self.convert_pdf_to_images(pdf_path, output_dir)
                
                results["success"].append({
                    "pdf_path": pdf_path,
                    "output_dir": output_dir,
                    "image_paths": image_paths,
                    "page_count": len(image_paths)
                })
                
            except Exception as e:
                logger.error(f"批量转换失败 {pdf_path}: {e}")
                results["failed"].append({
                    "pdf_path": pdf_path,
                    "error": str(e)
                })
        
        logger.info(f"批量转换完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
        return results
