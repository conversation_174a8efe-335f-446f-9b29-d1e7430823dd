"""
图像增强处理器
"""
from typing import Tuple, Optional
import numpy as np
import cv2
from PIL import Image, ImageEnhance, ImageFilter
from loguru import logger

from src.offerDiff.utils.image_utils import convert_to_grayscale


class ImageEnhancer:
    """图像增强处理器"""
    
    def __init__(self):
        self.default_dpi = 300
        self.target_size = None  # 如果设置，会调整图像尺寸
    
    def enhance_image(self, image: np.ndarray, enhancement_config: Optional[dict] = None) -> np.ndarray:
        """
        综合图像增强
        
        Args:
            image: 输入图像
            enhancement_config: 增强配置
            
        Returns:
            增强后的图像
        """
        try:
            if enhancement_config is None:
                enhancement_config = self._get_default_config()
            
            enhanced_image = image.copy()
            
            # 1. 去噪
            if enhancement_config.get("denoise", True):
                enhanced_image = self.denoise_image(enhanced_image)
                logger.debug("应用去噪处理")
            
            # 2. 倾斜校正
            if enhancement_config.get("deskew", True):
                enhanced_image = self.correct_skew(enhanced_image)
                logger.debug("应用倾斜校正")
            
            # 3. 对比度增强
            if enhancement_config.get("enhance_contrast", True):
                enhanced_image = self.enhance_contrast(enhanced_image)
                logger.debug("应用对比度增强")
            
            # 4. 亮度调整
            if enhancement_config.get("adjust_brightness", True):
                enhanced_image = self.adjust_brightness(enhanced_image)
                logger.debug("应用亮度调整")
            
            # 5. 锐化
            if enhancement_config.get("sharpen", True):
                enhanced_image = self.sharpen_image(enhanced_image)
                logger.debug("应用锐化处理")
            
            # 6. 分辨率标准化
            if enhancement_config.get("normalize_resolution", True):
                enhanced_image = self.normalize_resolution(enhanced_image)
                logger.debug("应用分辨率标准化")
            
            logger.info("图像增强处理完成")
            return enhanced_image
            
        except Exception as e:
            logger.error(f"图像增强失败: {e}")
            return image
    
    def denoise_image(self, image: np.ndarray, method: str = "bilateral") -> np.ndarray:
        """
        图像去噪
        
        Args:
            image: 输入图像
            method: 去噪方法 ("bilateral", "gaussian", "median", "nlm")
            
        Returns:
            去噪后的图像
        """
        try:
            if method == "bilateral":
                # 双边滤波 - 保持边缘的同时去噪
                return cv2.bilateralFilter(image, 9, 75, 75)
                
            elif method == "gaussian":
                # 高斯滤波
                return cv2.GaussianBlur(image, (5, 5), 0)
                
            elif method == "median":
                # 中值滤波 - 对椒盐噪声效果好
                return cv2.medianBlur(image, 5)
                
            elif method == "nlm":
                # 非局部均值去噪
                if len(image.shape) == 3:
                    return cv2.fastNlMeansDenoisingColored(image, None, 10, 10, 7, 21)
                else:
                    return cv2.fastNlMeansDenoising(image, None, 10, 7, 21)
            
            else:
                logger.warning(f"未知的去噪方法: {method}")
                return image
                
        except Exception as e:
            logger.error(f"去噪处理失败: {e}")
            return image
    
    def correct_skew(self, image: np.ndarray, max_angle: float = 10.0) -> np.ndarray:
        """
        倾斜校正
        
        Args:
            image: 输入图像
            max_angle: 最大校正角度
            
        Returns:
            校正后的图像
        """
        try:
            # 转换为灰度图像
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            else:
                gray = image.copy()
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # 霍夫变换检测直线
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            if lines is None:
                return image
            
            # 计算倾斜角度
            angles = []
            for rho, theta in lines[:20]:  # 只取前20条线
                angle = theta * 180 / np.pi
                # 转换为相对于水平线的角度
                if angle > 90:
                    angle = angle - 180
                if abs(angle) <= max_angle:
                    angles.append(angle)
            
            if not angles:
                return image
            
            # 使用中位数作为倾斜角度
            skew_angle = np.median(angles)
            
            # 如果角度太小，不进行校正
            if abs(skew_angle) < 0.5:
                return image
            
            # 旋转图像
            height, width = image.shape[:2]
            center = (width // 2, height // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, skew_angle, 1.0)
            
            # 计算新的边界
            cos_angle = abs(rotation_matrix[0, 0])
            sin_angle = abs(rotation_matrix[0, 1])
            new_width = int((height * sin_angle) + (width * cos_angle))
            new_height = int((height * cos_angle) + (width * sin_angle))
            
            # 调整旋转中心
            rotation_matrix[0, 2] += (new_width / 2) - center[0]
            rotation_matrix[1, 2] += (new_height / 2) - center[1]
            
            # 执行旋转
            rotated = cv2.warpAffine(image, rotation_matrix, (new_width, new_height), 
                                   flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
            
            logger.debug(f"倾斜校正完成，角度: {skew_angle:.2f}°")
            return rotated
            
        except Exception as e:
            logger.error(f"倾斜校正失败: {e}")
            return image
    
    def enhance_contrast(self, image: np.ndarray, method: str = "clahe") -> np.ndarray:
        """
        对比度增强
        
        Args:
            image: 输入图像
            method: 增强方法 ("clahe", "histogram_eq", "adaptive")
            
        Returns:
            增强后的图像
        """
        try:
            if method == "clahe":
                # CLAHE (Contrast Limited Adaptive Histogram Equalization)
                if len(image.shape) == 3:
                    # 彩色图像：在LAB色彩空间的L通道应用CLAHE
                    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    lab[:, :, 0] = clahe.apply(lab[:, :, 0])
                    return cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
                else:
                    # 灰度图像
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    return clahe.apply(image)
                    
            elif method == "histogram_eq":
                # 直方图均衡化
                if len(image.shape) == 3:
                    # 转换为YUV色彩空间，只对Y通道进行均衡化
                    yuv = cv2.cvtColor(image, cv2.COLOR_RGB2YUV)
                    yuv[:, :, 0] = cv2.equalizeHist(yuv[:, :, 0])
                    return cv2.cvtColor(yuv, cv2.COLOR_YUV2RGB)
                else:
                    return cv2.equalizeHist(image)
                    
            elif method == "adaptive":
                # 自适应对比度增强
                # 使用PIL进行处理
                pil_image = Image.fromarray(image)
                enhancer = ImageEnhance.Contrast(pil_image)
                enhanced = enhancer.enhance(1.5)  # 增强因子
                return np.array(enhanced)
            
            else:
                logger.warning(f"未知的对比度增强方法: {method}")
                return image
                
        except Exception as e:
            logger.error(f"对比度增强失败: {e}")
            return image
    
    def adjust_brightness(self, image: np.ndarray, target_mean: float = 128) -> np.ndarray:
        """
        亮度调整
        
        Args:
            image: 输入图像
            target_mean: 目标平均亮度
            
        Returns:
            调整后的图像
        """
        try:
            # 计算当前平均亮度
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            else:
                gray = image
            
            current_mean = np.mean(gray)
            
            # 计算调整量
            adjustment = target_mean - current_mean
            
            # 如果调整量很小，不进行处理
            if abs(adjustment) < 10:
                return image
            
            # 应用亮度调整
            adjusted = image.astype(np.float32) + adjustment
            adjusted = np.clip(adjusted, 0, 255).astype(np.uint8)
            
            logger.debug(f"亮度调整: {current_mean:.1f} -> {target_mean:.1f}")
            return adjusted
            
        except Exception as e:
            logger.error(f"亮度调整失败: {e}")
            return image
    
    def sharpen_image(self, image: np.ndarray, strength: float = 1.0) -> np.ndarray:
        """
        图像锐化
        
        Args:
            image: 输入图像
            strength: 锐化强度
            
        Returns:
            锐化后的图像
        """
        try:
            # 使用Unsharp Mask锐化
            # 1. 创建高斯模糊版本
            blurred = cv2.GaussianBlur(image, (0, 0), 1.0)
            
            # 2. 计算差值
            unsharp_mask = cv2.addWeighted(image, 1.0 + strength, blurred, -strength, 0)
            
            return unsharp_mask
            
        except Exception as e:
            logger.error(f"图像锐化失败: {e}")
            return image
    
    def normalize_resolution(self, image: np.ndarray, target_dpi: Optional[int] = None) -> np.ndarray:
        """
        分辨率标准化
        
        Args:
            image: 输入图像
            target_dpi: 目标DPI
            
        Returns:
            标准化后的图像
        """
        try:
            target_dpi = target_dpi or self.default_dpi
            
            # 如果设置了目标尺寸，调整图像大小
            if self.target_size:
                height, width = image.shape[:2]
                target_width, target_height = self.target_size
                
                # 计算缩放比例
                scale_x = target_width / width
                scale_y = target_height / height
                scale = min(scale_x, scale_y)  # 保持宽高比
                
                new_width = int(width * scale)
                new_height = int(height * scale)
                
                # 调整尺寸
                resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                
                # 如果需要，创建目标尺寸的画布
                if new_width != target_width or new_height != target_height:
                    canvas = np.ones((target_height, target_width, image.shape[2]), dtype=image.dtype) * 255
                    y_offset = (target_height - new_height) // 2
                    x_offset = (target_width - new_width) // 2
                    canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
                    return canvas
                
                return resized
            
            return image
            
        except Exception as e:
            logger.error(f"分辨率标准化失败: {e}")
            return image
    
    def _get_default_config(self) -> dict:
        """获取默认增强配置"""
        return {
            "denoise": True,
            "deskew": True,
            "enhance_contrast": True,
            "adjust_brightness": True,
            "sharpen": False,  # 默认不锐化，避免过度处理
            "normalize_resolution": True
        }
