"""
OCR结果结构化处理器
"""
import json
from typing import Dict, List, Optional, Any
from loguru import logger

from src.offerDiff.models.ocr_result import (
    OCRResult, OCRResultBuilder, TextBlock, Table, TableCell, 
    BoundingBox, TextType, ProcessingStatus
)
from src.offerDiff.ocr.post_processor import OCRPostProcessor


class ResultStructurer:
    """OCR结果结构化处理器"""
    
    def __init__(self):
        self.post_processor = OCRPostProcessor()
    
    def structure_ocr_result(self, raw_ocr_result: Dict, file_id: str, image_path: str) -> OCRResult:
        """
        将原始OCR结果结构化
        
        Args:
            raw_ocr_result: 原始OCR结果
            file_id: 文件ID
            image_path: 图像路径
            
        Returns:
            结构化的OCR结果
        """
        try:
            # 创建结果构建器
            builder = OCRResultBuilder(file_id, image_path)
            
            # 后处理原始结果
            processed_result = self.post_processor.process_ocr_result(raw_ocr_result)
            
            if not processed_result.get("success", False):
                return builder.build(
                    status=ProcessingStatus.FAILED,
                    error_message=processed_result.get("error", "OCR处理失败")
                )
            
            # 结构化文本块
            self._structure_text_blocks(processed_result, builder)
            
            # 结构化表格
            self._structure_tables(processed_result, builder)
            
            # 构建最终结果
            result = builder.build(status=ProcessingStatus.COMPLETED)
            
            logger.info(f"OCR结果结构化完成: {len(result.text_blocks)}个文本块, {len(result.tables)}个表格")
            return result
            
        except Exception as e:
            logger.error(f"OCR结果结构化失败: {e}")
            builder = OCRResultBuilder(file_id, image_path)
            return builder.build(
                status=ProcessingStatus.FAILED,
                error_message=str(e)
            )
    
    def _structure_text_blocks(self, processed_result: Dict, builder: OCRResultBuilder):
        """结构化文本块"""
        try:
            text_blocks_data = processed_result.get("text_blocks", [])
            
            for block_data in text_blocks_data:
                # 提取基本信息
                text = block_data.get("text", "")
                original_text = block_data.get("original_text", text)
                confidence = block_data.get("confidence", 0.0)
                text_type = self._parse_text_type(block_data.get("text_type", "text"))
                
                # 提取边界框
                bbox_data = block_data.get("bbox", {})
                bbox = BoundingBox(
                    x1=bbox_data.get("x1", 0),
                    y1=bbox_data.get("y1", 0),
                    x2=bbox_data.get("x2", 0),
                    y2=bbox_data.get("y2", 0)
                )
                
                # 提取多边形
                polygon = block_data.get("polygon", [])
                
                # 提取验证信息
                validation_info = block_data.get("validation", {})
                
                # 添加到构建器
                builder.add_text_block(
                    text=text,
                    original_text=original_text,
                    confidence=confidence,
                    text_type=text_type,
                    bbox=bbox,
                    polygon=polygon,
                    validation_info=validation_info
                )
                
        except Exception as e:
            logger.error(f"结构化文本块失败: {e}")
    
    def _structure_tables(self, processed_result: Dict, builder: OCRResultBuilder):
        """结构化表格"""
        try:
            tables_data = processed_result.get("tables", [])
            
            for table_data in tables_data:
                # 提取表格边界框
                bbox_data = table_data.get("bbox", {})
                table_bbox = BoundingBox(
                    x1=bbox_data.get("x1", 0),
                    y1=bbox_data.get("y1", 0),
                    x2=bbox_data.get("x2", 0),
                    y2=bbox_data.get("y2", 0)
                )
                
                # 结构化单元格
                cells = self._structure_table_cells(table_data.get("cells", []))
                
                # 计算行列数
                rows = max([cell.row for cell in cells]) + 1 if cells else 0
                columns = max([cell.col for cell in cells]) + 1 if cells else 0
                
                # 提取表格属性
                table_attrs = {
                    "has_header": table_data.get("structure", {}).get("has_header", False),
                    "header_row": table_data.get("structure", {}).get("header_row"),
                    "table_type": table_data.get("structure", {}).get("table_type", "standard"),
                    "html": table_data.get("html", ""),
                    "confidence": table_data.get("confidence", 0.0),
                    "structure_confidence": table_data.get("structure", {}).get("confidence", 0.0),
                    "consistency_issues": table_data.get("consistency", {}).get("issues", [])
                }
                
                # 添加到构建器
                builder.add_table(
                    bbox=table_bbox,
                    cells=cells,
                    rows=rows,
                    columns=columns,
                    **table_attrs
                )
                
        except Exception as e:
            logger.error(f"结构化表格失败: {e}")
    
    def _structure_table_cells(self, cells_data: List[Dict]) -> List[TableCell]:
        """结构化表格单元格"""
        cells = []
        
        try:
            for i, cell_data in enumerate(cells_data):
                # 提取基本信息
                text = cell_data.get("text", "")
                original_text = cell_data.get("original_text", text)
                confidence = cell_data.get("confidence", 0.0)
                text_type = self._parse_text_type(cell_data.get("text_type", "text"))
                
                # 提取位置信息
                row = cell_data.get("row", i // 10)  # 默认估算
                col = cell_data.get("col", i % 10)   # 默认估算
                
                # 提取边界框
                bbox_data = cell_data.get("bbox", {})
                bbox = BoundingBox(
                    x1=bbox_data.get("x1", 0),
                    y1=bbox_data.get("y1", 0),
                    x2=bbox_data.get("x2", 0),
                    y2=bbox_data.get("y2", 0)
                )
                
                # 提取合并信息
                is_merged = cell_data.get("is_merged", False)
                merged_cells = cell_data.get("merged_cells", [])
                rowspan = cell_data.get("rowspan", 1)
                colspan = cell_data.get("colspan", 1)
                
                # 提取验证信息
                validation_info = cell_data.get("validation", {})
                is_valid = validation_info.get("is_valid", True)
                validation_issues = validation_info.get("issues", [])
                
                # 创建单元格
                cell = TableCell(
                    id="",  # 将在__post_init__中生成
                    row=row,
                    col=col,
                    text=text,
                    original_text=original_text,
                    confidence=confidence,
                    text_type=text_type,
                    bbox=bbox,
                    is_merged=is_merged,
                    merged_cells=merged_cells,
                    rowspan=rowspan,
                    colspan=colspan,
                    is_valid=is_valid,
                    validation_issues=validation_issues
                )
                
                cells.append(cell)
                
        except Exception as e:
            logger.error(f"结构化表格单元格失败: {e}")
        
        return cells
    
    def _parse_text_type(self, text_type_str: str) -> TextType:
        """解析文本类型"""
        try:
            # 标准化文本类型字符串
            text_type_str = text_type_str.lower().strip()
            
            # 映射到枚举
            type_mapping = {
                "text": TextType.TEXT,
                "number": TextType.NUMBER,
                "integer": TextType.NUMBER,
                "decimal": TextType.NUMBER,
                "currency": TextType.CURRENCY,
                "percentage": TextType.PERCENTAGE,
                "date": TextType.DATE,
                "time": TextType.TIME,
                "phone": TextType.PHONE,
                "email": TextType.EMAIL,
                "financial_term": TextType.FINANCIAL_TERM
            }
            
            return type_mapping.get(text_type_str, TextType.TEXT)
            
        except Exception as e:
            logger.error(f"解析文本类型失败: {e}")
            return TextType.TEXT
    
    def export_to_json(self, ocr_result: OCRResult, output_path: str) -> bool:
        """导出为JSON文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(ocr_result.to_json())
            
            logger.info(f"OCR结果已导出到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            return False
    
    def export_to_structured_data(self, ocr_result: OCRResult) -> Dict:
        """导出为结构化数据"""
        try:
            structured_data = {
                "metadata": {
                    "id": ocr_result.id,
                    "file_id": ocr_result.file_id,
                    "image_path": ocr_result.image_path,
                    "status": ocr_result.status.value,
                    "processing_time": ocr_result.processing_time,
                    "created_at": ocr_result.created_at.isoformat() if ocr_result.created_at else None,
                    "statistics": {
                        "total_text_blocks": ocr_result.total_text_blocks,
                        "total_tables": ocr_result.total_tables,
                        "total_cells": ocr_result.total_cells,
                        "overall_confidence": ocr_result.overall_confidence
                    }
                },
                "content": {
                    "full_text": ocr_result.full_text,
                    "text_blocks": [],
                    "tables": [],
                    "financial_data": ocr_result.get_financial_data()
                }
            }
            
            # 添加文本块
            for block in ocr_result.text_blocks:
                block_data = {
                    "id": block.id,
                    "text": block.text,
                    "type": block.text_type.value,
                    "confidence": block.confidence,
                    "position": {
                        "x": block.bbox.x1,
                        "y": block.bbox.y1,
                        "width": block.bbox.width,
                        "height": block.bbox.height
                    },
                    "is_valid": block.is_valid
                }
                structured_data["content"]["text_blocks"].append(block_data)
            
            # 添加表格
            for table in ocr_result.tables:
                table_data = {
                    "id": table.id,
                    "rows": table.rows,
                    "columns": table.columns,
                    "has_header": table.has_header,
                    "confidence": table.confidence,
                    "position": {
                        "x": table.bbox.x1,
                        "y": table.bbox.y1,
                        "width": table.bbox.width,
                        "height": table.bbox.height
                    },
                    "cells": []
                }
                
                # 添加单元格
                for cell in table.cells:
                    cell_data = {
                        "id": cell.id,
                        "row": cell.row,
                        "col": cell.col,
                        "text": cell.text,
                        "type": cell.text_type.value,
                        "confidence": cell.confidence,
                        "is_merged": cell.is_merged,
                        "rowspan": cell.rowspan,
                        "colspan": cell.colspan
                    }
                    table_data["cells"].append(cell_data)
                
                structured_data["content"]["tables"].append(table_data)
            
            return structured_data
            
        except Exception as e:
            logger.error(f"导出结构化数据失败: {e}")
            return {}
    
    def create_comparison_structure(self, ocr_result1: OCRResult, ocr_result2: OCRResult) -> Dict:
        """创建用于比对的结构化数据"""
        try:
            comparison_data = {
                "file1": {
                    "id": ocr_result1.id,
                    "confidence": ocr_result1.overall_confidence,
                    "content": self._extract_comparable_content(ocr_result1)
                },
                "file2": {
                    "id": ocr_result2.id,
                    "confidence": ocr_result2.overall_confidence,
                    "content": self._extract_comparable_content(ocr_result2)
                },
                "metadata": {
                    "created_at": ocr_result1.created_at.isoformat() if ocr_result1.created_at else None,
                    "comparison_ready": True
                }
            }
            
            return comparison_data
            
        except Exception as e:
            logger.error(f"创建比对结构失败: {e}")
            return {}
    
    def _extract_comparable_content(self, ocr_result: OCRResult) -> Dict:
        """提取可比对的内容"""
        try:
            content = {
                "text_elements": [],
                "table_elements": [],
                "financial_elements": [],
                "layout_elements": []
            }
            
            # 提取文本元素
            for block in ocr_result.text_blocks:
                if block.is_valid and block.confidence > 0.5:
                    element = {
                        "text": block.text,
                        "type": block.text_type.value,
                        "position": (block.bbox.x1, block.bbox.y1, block.bbox.x2, block.bbox.y2),
                        "confidence": block.confidence
                    }
                    content["text_elements"].append(element)
            
            # 提取表格元素
            for table in ocr_result.tables:
                table_element = {
                    "rows": table.rows,
                    "columns": table.columns,
                    "position": (table.bbox.x1, table.bbox.y1, table.bbox.x2, table.bbox.y2),
                    "cells": []
                }
                
                for cell in table.cells:
                    if cell.is_valid and cell.confidence > 0.5:
                        cell_element = {
                            "row": cell.row,
                            "col": cell.col,
                            "text": cell.text,
                            "type": cell.text_type.value,
                            "confidence": cell.confidence
                        }
                        table_element["cells"].append(cell_element)
                
                content["table_elements"].append(table_element)
            
            # 提取财务元素
            financial_data = ocr_result.get_financial_data()
            content["financial_elements"] = financial_data
            
            # 提取布局元素（简化版）
            layout_elements = []
            all_elements = ocr_result.text_blocks + [cell for table in ocr_result.tables for cell in table.cells]
            
            for element in all_elements:
                if hasattr(element, 'bbox') and element.bbox:
                    layout_element = {
                        "type": "text_block" if isinstance(element, TextBlock) else "table_cell",
                        "position": (element.bbox.x1, element.bbox.y1, element.bbox.x2, element.bbox.y2),
                        "area": element.bbox.area
                    }
                    layout_elements.append(layout_element)
            
            content["layout_elements"] = layout_elements
            
            return content
            
        except Exception as e:
            logger.error(f"提取可比对内容失败: {e}")
            return {}
