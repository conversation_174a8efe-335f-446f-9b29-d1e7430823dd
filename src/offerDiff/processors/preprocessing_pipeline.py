"""
图像预处理流水线
"""
import time
from typing import List, Dict, Optional, Callable, Any
import numpy as np
from loguru import logger

from src.offerDiff.processors.pdf_converter import PDFConverter
from src.offerDiff.processors.quality_checker import QualityChecker
from src.offerDiff.processors.image_enhancer import ImageEnhancer
from src.offerDiff.utils.image_utils import load_image, save_image
from src.offerDiff.utils.progress_tracker import ProgressTracker


class PreprocessingPipeline:
    """图像预处理流水线"""
    
    def __init__(self):
        self.pdf_converter = PDFConverter()
        self.quality_checker = QualityChecker()
        self.image_enhancer = ImageEnhancer()
        self.progress_tracker = ProgressTracker()
        
        # 默认处理步骤
        self.default_steps = [
            ("pdf_conversion", self._step_pdf_conversion),
            ("quality_check", self._step_quality_check),
            ("image_enhancement", self._step_image_enhancement),
            ("final_validation", self._step_final_validation)
        ]
    
    def process_pdf(
        self, 
        pdf_path: str, 
        output_dir: str,
        config: Optional[Dict] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """
        处理PDF文件的完整流水线
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 输出目录
            config: 处理配置
            progress_callback: 进度回调函数
            
        Returns:
            处理结果字典
        """
        try:
            start_time = time.time()
            
            # 初始化配置
            if config is None:
                config = self._get_default_config()
            
            # 初始化进度跟踪
            self.progress_tracker.start("PDF预处理", len(self.default_steps))
            
            # 初始化结果
            result = {
                "success": False,
                "pdf_path": pdf_path,
                "output_dir": output_dir,
                "processing_time": 0,
                "steps_completed": [],
                "steps_failed": [],
                "image_paths": [],
                "quality_reports": [],
                "enhanced_paths": [],
                "error": None
            }
            
            logger.info(f"开始处理PDF: {pdf_path}")
            
            # 执行处理步骤
            context = {
                "pdf_path": pdf_path,
                "output_dir": output_dir,
                "config": config,
                "result": result
            }
            
            for step_name, step_func in self.default_steps:
                try:
                    logger.info(f"执行步骤: {step_name}")
                    
                    # 执行步骤
                    step_result = step_func(context)
                    
                    # 更新上下文
                    context.update(step_result)
                    
                    # 记录成功步骤
                    result["steps_completed"].append(step_name)
                    
                    # 更新进度
                    progress = self.progress_tracker.update_step(step_name)
                    if progress_callback:
                        progress_callback(progress, f"完成步骤: {step_name}")
                    
                    logger.info(f"步骤 {step_name} 完成")
                    
                except Exception as e:
                    logger.error(f"步骤 {step_name} 失败: {e}")
                    result["steps_failed"].append({
                        "step": step_name,
                        "error": str(e)
                    })
                    
                    # 根据配置决定是否继续
                    if config.get("stop_on_error", False):
                        raise
            
            # 计算处理时间
            result["processing_time"] = time.time() - start_time
            
            # 判断是否成功
            if not result["steps_failed"] and result["enhanced_paths"]:
                result["success"] = True
                logger.info(f"PDF处理完成: {pdf_path}, 耗时: {result['processing_time']:.2f}秒")
            else:
                logger.warning(f"PDF处理部分失败: {pdf_path}")
            
            return result
            
        except Exception as e:
            logger.error(f"PDF处理流水线失败: {e}")
            result["error"] = str(e)
            result["processing_time"] = time.time() - start_time
            return result
    
    def _step_pdf_conversion(self, context: Dict) -> Dict:
        """PDF转换步骤"""
        pdf_path = context["pdf_path"]
        output_dir = context["output_dir"]
        config = context["config"]
        
        # 验证PDF
        is_valid, error_msg = self.pdf_converter.validate_pdf(pdf_path)
        if not is_valid:
            raise ValueError(f"PDF验证失败: {error_msg}")
        
        # 转换PDF为图像
        image_paths = self.pdf_converter.convert_pdf_to_images(
            pdf_path, 
            output_dir,
            dpi=config.get("conversion_dpi", 300)
        )
        
        # 获取PDF信息
        pdf_info = self.pdf_converter.get_pdf_info(pdf_path)
        
        return {
            "image_paths": image_paths,
            "pdf_info": pdf_info
        }
    
    def _step_quality_check(self, context: Dict) -> Dict:
        """质量检查步骤"""
        image_paths = context["image_paths"]
        config = context["config"]
        
        quality_reports = []
        
        for image_path in image_paths:
            try:
                # 检查图像质量
                quality_report = self.quality_checker.check_image_quality(image_path)
                quality_reports.append({
                    "image_path": image_path,
                    "quality": quality_report
                })
                
                # 检查是否满足最低质量要求
                min_score = config.get("min_quality_score", 60)
                if quality_report.get("overall_score", 0) < min_score:
                    logger.warning(f"图像质量不达标: {image_path}, 评分: {quality_report.get('overall_score', 0)}")
                
            except Exception as e:
                logger.error(f"质量检查失败 {image_path}: {e}")
                quality_reports.append({
                    "image_path": image_path,
                    "quality": {"error": str(e), "overall_score": 0}
                })
        
        return {
            "quality_reports": quality_reports
        }
    
    def _step_image_enhancement(self, context: Dict) -> Dict:
        """图像增强步骤"""
        image_paths = context["image_paths"]
        quality_reports = context["quality_reports"]
        output_dir = context["output_dir"]
        config = context["config"]
        
        enhanced_paths = []
        
        for i, image_path in enumerate(image_paths):
            try:
                # 加载图像
                image = load_image(image_path)
                
                # 获取质量报告
                quality_report = quality_reports[i]["quality"] if i < len(quality_reports) else {}
                
                # 根据质量报告调整增强配置
                enhancement_config = self._adapt_enhancement_config(quality_report, config)
                
                # 执行图像增强
                enhanced_image = self.image_enhancer.enhance_image(image, enhancement_config)
                
                # 保存增强后的图像
                enhanced_path = image_path.replace(".png", "_enhanced.png")
                if save_image(enhanced_image, enhanced_path):
                    enhanced_paths.append(enhanced_path)
                    logger.debug(f"图像增强完成: {enhanced_path}")
                else:
                    logger.error(f"保存增强图像失败: {enhanced_path}")
                
            except Exception as e:
                logger.error(f"图像增强失败 {image_path}: {e}")
        
        return {
            "enhanced_paths": enhanced_paths
        }
    
    def _step_final_validation(self, context: Dict) -> Dict:
        """最终验证步骤"""
        enhanced_paths = context["enhanced_paths"]
        config = context["config"]
        
        validation_results = []
        
        for enhanced_path in enhanced_paths:
            try:
                # 重新检查增强后的图像质量
                final_quality = self.quality_checker.check_image_quality(enhanced_path)
                
                validation_result = {
                    "image_path": enhanced_path,
                    "final_quality": final_quality,
                    "passed": final_quality.get("overall_score", 0) >= config.get("min_final_score", 70)
                }
                
                validation_results.append(validation_result)
                
                if validation_result["passed"]:
                    logger.info(f"最终验证通过: {enhanced_path}")
                else:
                    logger.warning(f"最终验证未通过: {enhanced_path}")
                
            except Exception as e:
                logger.error(f"最终验证失败 {enhanced_path}: {e}")
                validation_results.append({
                    "image_path": enhanced_path,
                    "final_quality": {"error": str(e)},
                    "passed": False
                })
        
        return {
            "validation_results": validation_results
        }
    
    def _adapt_enhancement_config(self, quality_report: Dict, base_config: Dict) -> Dict:
        """根据质量报告调整增强配置"""
        enhancement_config = base_config.get("enhancement", {}).copy()
        
        try:
            # 根据清晰度调整锐化
            sharpness_score = quality_report.get("sharpness", {}).get("score", 100)
            if sharpness_score < 70:
                enhancement_config["sharpen"] = True
            
            # 根据对比度调整对比度增强
            contrast_score = quality_report.get("contrast", {}).get("score", 100)
            if contrast_score < 70:
                enhancement_config["enhance_contrast"] = True
            
            # 根据噪声水平调整去噪
            noise_score = quality_report.get("noise_level", {}).get("score", 100)
            if noise_score < 70:
                enhancement_config["denoise"] = True
            
            # 根据倾斜角度调整校正
            skew_angle = abs(quality_report.get("skew_angle", {}).get("skew_angle", 0))
            if skew_angle > 1.0:
                enhancement_config["deskew"] = True
            
            # 根据亮度调整亮度校正
            brightness_score = quality_report.get("brightness", {}).get("score", 100)
            if brightness_score < 70:
                enhancement_config["adjust_brightness"] = True
            
        except Exception as e:
            logger.error(f"调整增强配置失败: {e}")
        
        return enhancement_config
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "conversion_dpi": 300,
            "min_quality_score": 60,
            "min_final_score": 70,
            "stop_on_error": False,
            "enhancement": {
                "denoise": True,
                "deskew": True,
                "enhance_contrast": True,
                "adjust_brightness": True,
                "sharpen": False,
                "normalize_resolution": True
            }
        }
    
    def batch_process(
        self, 
        pdf_paths: List[str], 
        output_base_dir: str,
        config: Optional[Dict] = None,
        progress_callback: Optional[Callable] = None
    ) -> Dict:
        """
        批量处理PDF文件
        
        Args:
            pdf_paths: PDF文件路径列表
            output_base_dir: 输出基础目录
            config: 处理配置
            progress_callback: 进度回调函数
            
        Returns:
            批量处理结果
        """
        try:
            start_time = time.time()
            
            results = {
                "success": [],
                "failed": [],
                "total": len(pdf_paths),
                "total_time": 0
            }
            
            for i, pdf_path in enumerate(pdf_paths):
                try:
                    # 为每个PDF创建单独的输出目录
                    import os
                    pdf_name = os.path.splitext(os.path.basename(pdf_path))[0]
                    output_dir = os.path.join(output_base_dir, pdf_name)
                    
                    # 处理PDF
                    result = self.process_pdf(pdf_path, output_dir, config)
                    
                    if result["success"]:
                        results["success"].append(result)
                    else:
                        results["failed"].append(result)
                    
                    # 更新总体进度
                    if progress_callback:
                        overall_progress = int((i + 1) / len(pdf_paths) * 100)
                        progress_callback(overall_progress, f"处理完成: {i + 1}/{len(pdf_paths)}")
                    
                except Exception as e:
                    logger.error(f"批量处理失败 {pdf_path}: {e}")
                    results["failed"].append({
                        "pdf_path": pdf_path,
                        "error": str(e),
                        "success": False
                    })
            
            results["total_time"] = time.time() - start_time
            
            logger.info(f"批量处理完成: 成功 {len(results['success'])}, 失败 {len(results['failed'])}")
            return results
            
        except Exception as e:
            logger.error(f"批量处理流水线失败: {e}")
            return {
                "success": [],
                "failed": pdf_paths,
                "total": len(pdf_paths),
                "total_time": time.time() - start_time,
                "error": str(e)
            }
