"""
图像质量检测器
"""
import math
from typing import Dict, Tu<PERSON>, Optional
import numpy as np
import cv2
from loguru import logger

from src.offerDiff.utils.image_utils import load_image, convert_to_grayscale


class QualityChecker:
    """图像质量检测器"""
    
    def __init__(self):
        self.min_resolution = (800, 600)  # 最小分辨率
        self.min_dpi = 150  # 最小DPI
        self.blur_threshold = 100  # 模糊度阈值
        self.noise_threshold = 30  # 噪声阈值
        self.contrast_threshold = 50  # 对比度阈值
    
    def check_image_quality(self, image_path: str) -> Dict:
        """
        检查图像质量
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            质量检测结果字典
        """
        try:
            # 加载图像
            image = load_image(image_path)
            gray_image = convert_to_grayscale(image)
            
            # 执行各项质量检测
            results = {
                "overall_score": 0,
                "resolution": self._check_resolution(image),
                "sharpness": self._check_sharpness(gray_image),
                "contrast": self._check_contrast(gray_image),
                "noise_level": self._check_noise_level(gray_image),
                "brightness": self._check_brightness(gray_image),
                "skew_angle": self._detect_skew_angle(gray_image),
                "recommendations": []
            }
            
            # 计算综合评分
            results["overall_score"] = self._calculate_overall_score(results)
            
            # 生成改进建议
            results["recommendations"] = self._generate_recommendations(results)
            
            logger.info(f"图像质量检测完成: {image_path}, 评分: {results['overall_score']}")
            return results
            
        except Exception as e:
            logger.error(f"图像质量检测失败: {e}")
            return {"error": str(e), "overall_score": 0}
    
    def _check_resolution(self, image: np.ndarray) -> Dict:
        """检查分辨率"""
        height, width = image.shape[:2]
        
        # 计算像素密度
        total_pixels = width * height
        
        result = {
            "width": width,
            "height": height,
            "total_pixels": total_pixels,
            "meets_minimum": width >= self.min_resolution[0] and height >= self.min_resolution[1],
            "score": 0
        }
        
        # 评分逻辑
        if total_pixels >= 2073600:  # 1920x1080
            result["score"] = 100
        elif total_pixels >= 1228800:  # 1280x960
            result["score"] = 85
        elif total_pixels >= 480000:  # 800x600
            result["score"] = 70
        else:
            result["score"] = 40
        
        return result
    
    def _check_sharpness(self, gray_image: np.ndarray) -> Dict:
        """检查清晰度（使用拉普拉斯算子）"""
        try:
            # 计算拉普拉斯方差
            laplacian = cv2.Laplacian(gray_image, cv2.CV_64F)
            variance = laplacian.var()
            
            result = {
                "variance": float(variance),
                "is_sharp": variance > self.blur_threshold,
                "score": 0
            }
            
            # 评分逻辑
            if variance > 500:
                result["score"] = 100
            elif variance > 200:
                result["score"] = 85
            elif variance > self.blur_threshold:
                result["score"] = 70
            elif variance > 50:
                result["score"] = 50
            else:
                result["score"] = 20
            
            return result
            
        except Exception as e:
            logger.error(f"清晰度检测失败: {e}")
            return {"error": str(e), "score": 0}
    
    def _check_contrast(self, gray_image: np.ndarray) -> Dict:
        """检查对比度"""
        try:
            # 计算标准差作为对比度指标
            std_dev = np.std(gray_image)
            
            # 计算动态范围
            min_val = np.min(gray_image)
            max_val = np.max(gray_image)
            dynamic_range = max_val - min_val
            
            result = {
                "std_deviation": float(std_dev),
                "dynamic_range": int(dynamic_range),
                "min_value": int(min_val),
                "max_value": int(max_val),
                "good_contrast": std_dev > self.contrast_threshold,
                "score": 0
            }
            
            # 评分逻辑
            if std_dev > 80:
                result["score"] = 100
            elif std_dev > 60:
                result["score"] = 85
            elif std_dev > self.contrast_threshold:
                result["score"] = 70
            elif std_dev > 30:
                result["score"] = 50
            else:
                result["score"] = 20
            
            return result
            
        except Exception as e:
            logger.error(f"对比度检测失败: {e}")
            return {"error": str(e), "score": 0}
    
    def _check_noise_level(self, gray_image: np.ndarray) -> Dict:
        """检查噪声水平"""
        try:
            # 使用高斯模糊后的差值来估计噪声
            blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)
            noise = cv2.absdiff(gray_image, blurred)
            noise_level = np.mean(noise)
            
            result = {
                "noise_level": float(noise_level),
                "low_noise": noise_level < self.noise_threshold,
                "score": 0
            }
            
            # 评分逻辑（噪声越低分数越高）
            if noise_level < 10:
                result["score"] = 100
            elif noise_level < 20:
                result["score"] = 85
            elif noise_level < self.noise_threshold:
                result["score"] = 70
            elif noise_level < 50:
                result["score"] = 50
            else:
                result["score"] = 20
            
            return result
            
        except Exception as e:
            logger.error(f"噪声检测失败: {e}")
            return {"error": str(e), "score": 0}
    
    def _check_brightness(self, gray_image: np.ndarray) -> Dict:
        """检查亮度"""
        try:
            mean_brightness = np.mean(gray_image)
            
            result = {
                "mean_brightness": float(mean_brightness),
                "is_optimal": 80 <= mean_brightness <= 180,
                "score": 0
            }
            
            # 评分逻辑（适中亮度得分最高）
            if 100 <= mean_brightness <= 160:
                result["score"] = 100
            elif 80 <= mean_brightness <= 180:
                result["score"] = 85
            elif 60 <= mean_brightness <= 200:
                result["score"] = 70
            elif 40 <= mean_brightness <= 220:
                result["score"] = 50
            else:
                result["score"] = 20
            
            return result
            
        except Exception as e:
            logger.error(f"亮度检测失败: {e}")
            return {"error": str(e), "score": 0}
    
    def _detect_skew_angle(self, gray_image: np.ndarray) -> Dict:
        """检测倾斜角度"""
        try:
            # 边缘检测
            edges = cv2.Canny(gray_image, 50, 150, apertureSize=3)
            
            # 霍夫变换检测直线
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
            
            angles = []
            if lines is not None:
                for rho, theta in lines[:20]:  # 只取前20条线
                    angle = theta * 180 / np.pi
                    # 转换为相对于水平线的角度
                    if angle > 90:
                        angle = angle - 180
                    angles.append(angle)
            
            # 计算平均倾斜角度
            if angles:
                skew_angle = np.median(angles)
            else:
                skew_angle = 0
            
            result = {
                "skew_angle": float(skew_angle),
                "needs_correction": abs(skew_angle) > 1.0,
                "score": 0
            }
            
            # 评分逻辑（倾斜角度越小分数越高）
            abs_angle = abs(skew_angle)
            if abs_angle < 0.5:
                result["score"] = 100
            elif abs_angle < 1.0:
                result["score"] = 85
            elif abs_angle < 2.0:
                result["score"] = 70
            elif abs_angle < 5.0:
                result["score"] = 50
            else:
                result["score"] = 20
            
            return result
            
        except Exception as e:
            logger.error(f"倾斜检测失败: {e}")
            return {"error": str(e), "score": 0, "skew_angle": 0}
    
    def _calculate_overall_score(self, results: Dict) -> int:
        """计算综合评分"""
        try:
            # 权重配置
            weights = {
                "resolution": 0.15,
                "sharpness": 0.25,
                "contrast": 0.20,
                "noise_level": 0.15,
                "brightness": 0.15,
                "skew_angle": 0.10
            }
            
            total_score = 0
            total_weight = 0
            
            for metric, weight in weights.items():
                if metric in results and "score" in results[metric]:
                    total_score += results[metric]["score"] * weight
                    total_weight += weight
            
            if total_weight > 0:
                return int(total_score / total_weight)
            else:
                return 0
                
        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 0
    
    def _generate_recommendations(self, results: Dict) -> list:
        """生成改进建议"""
        recommendations = []
        
        try:
            # 分辨率建议
            if results.get("resolution", {}).get("score", 0) < 70:
                recommendations.append("建议提高扫描分辨率至300DPI或更高")
            
            # 清晰度建议
            if results.get("sharpness", {}).get("score", 0) < 70:
                recommendations.append("图像模糊，建议重新扫描或调整扫描仪焦距")
            
            # 对比度建议
            if results.get("contrast", {}).get("score", 0) < 70:
                recommendations.append("对比度不足，建议调整扫描仪对比度设置")
            
            # 噪声建议
            if results.get("noise_level", {}).get("score", 0) < 70:
                recommendations.append("图像噪声较多，建议清洁扫描仪或调整扫描设置")
            
            # 亮度建议
            brightness_score = results.get("brightness", {}).get("score", 0)
            if brightness_score < 70:
                mean_brightness = results.get("brightness", {}).get("mean_brightness", 128)
                if mean_brightness < 80:
                    recommendations.append("图像过暗，建议增加扫描亮度")
                elif mean_brightness > 180:
                    recommendations.append("图像过亮，建议降低扫描亮度")
            
            # 倾斜建议
            if results.get("skew_angle", {}).get("needs_correction", False):
                angle = results.get("skew_angle", {}).get("skew_angle", 0)
                recommendations.append(f"检测到倾斜角度 {angle:.1f}°，建议重新扫描或进行倾斜校正")
            
            if not recommendations:
                recommendations.append("图像质量良好，无需特殊处理")
            
        except Exception as e:
            logger.error(f"生成建议失败: {e}")
            recommendations.append("质量分析出现错误，建议人工检查")
        
        return recommendations
