"""
OCR后处理器
"""
import re
import json
from typing import List, Dict, Tuple, Optional, Any
from loguru import logger

from src.offerDiff.utils.text_validator import TextValidator


class OCRPostProcessor:
    """OCR后处理器"""
    
    def __init__(self):
        self.text_validator = TextValidator()
        self.confidence_threshold = 0.5
        self.load_dictionaries()
    
    def load_dictionaries(self):
        """加载词典和规则"""
        try:
            # 金融术语词典
            self.financial_terms = {
                "金额", "总价", "单价", "数量", "小计", "合计", "税率", "税额",
                "含税", "不含税", "人民币", "元", "万元", "千元", "美元", "欧元"
            }
            
            # 数字格式模式
            self.number_patterns = {
                "integer": r"^\d+$",
                "decimal": r"^\d+\.\d+$",
                "currency": r"^[¥$€£]?\s*\d{1,3}(,\d{3})*(\.\d{2})?$",
                "percentage": r"^\d+(\.\d+)?%$",
                "phone": r"^1[3-9]\d{9}$",
                "date": r"^\d{4}[-/]\d{1,2}[-/]\d{1,2}$"
            }
            
            # 常见错误字符映射
            self.char_corrections = {
                "０": "0", "１": "1", "２": "2", "３": "3", "４": "4",
                "５": "5", "６": "6", "７": "7", "８": "8", "９": "9",
                "，": ",", "。": ".", "；": ";", "：": ":",
                "？": "?", "！": "!", "（": "(", "）": ")",
                "【": "[", "】": "]", "《": "<", "》": ">",
                "Ｏ": "0", "ｏ": "0", "Ｉ": "1", "ｌ": "1",
                "Ｓ": "5", "ｓ": "5", "Ｇ": "6", "ｇ": "6"
            }
            
            logger.info("词典和规则加载完成")
            
        except Exception as e:
            logger.error(f"加载词典失败: {e}")
    
    def process_ocr_result(self, ocr_result: Dict) -> Dict:
        """
        处理OCR识别结果
        
        Args:
            ocr_result: OCR原始结果
            
        Returns:
            处理后的结果
        """
        try:
            if not ocr_result.get("success", False):
                return ocr_result
            
            # 处理文本块
            processed_blocks = []
            for block in ocr_result.get("text_blocks", []):
                processed_block = self._process_text_block(block)
                if processed_block:
                    processed_blocks.append(processed_block)
            
            # 处理表格
            processed_tables = []
            for table in ocr_result.get("tables", []):
                processed_table = self._process_table(table)
                if processed_table:
                    processed_tables.append(processed_table)
            
            # 重新计算置信度
            overall_confidence = self._calculate_overall_confidence(processed_blocks, processed_tables)
            
            # 生成处理后的完整文本
            full_text = self._generate_full_text(processed_blocks, processed_tables)
            
            processed_result = {
                "success": True,
                "text_blocks": processed_blocks,
                "tables": processed_tables,
                "full_text": full_text,
                "confidence": overall_confidence,
                "processing_info": {
                    "original_blocks": len(ocr_result.get("text_blocks", [])),
                    "processed_blocks": len(processed_blocks),
                    "original_tables": len(ocr_result.get("tables", [])),
                    "processed_tables": len(processed_tables),
                    "confidence_improved": overall_confidence > ocr_result.get("confidence", 0)
                }
            }
            
            logger.info(f"OCR后处理完成: {len(processed_blocks)}个文本块, {len(processed_tables)}个表格")
            return processed_result
            
        except Exception as e:
            logger.error(f"OCR后处理失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text_blocks": [],
                "tables": [],
                "full_text": "",
                "confidence": 0.0
            }
    
    def _process_text_block(self, block: Dict) -> Optional[Dict]:
        """处理单个文本块"""
        try:
            original_text = block.get("text", "")
            if not original_text.strip():
                return None
            
            # 文本清理和校正
            cleaned_text = self._clean_text(original_text)
            
            # 字符校正
            corrected_text = self._correct_characters(cleaned_text)
            
            # 格式识别和标准化
            formatted_text, text_type = self._format_text(corrected_text)
            
            # 拼写检查（针对英文）
            spell_checked_text = self._spell_check(formatted_text)
            
            # 验证文本合理性
            validation_result = self.text_validator.validate_text(spell_checked_text, text_type)
            
            # 计算处理后的置信度
            processed_confidence = self._calculate_processed_confidence(
                block.get("confidence", 0.0),
                validation_result
            )
            
            # 如果置信度太低，过滤掉
            if processed_confidence < self.confidence_threshold:
                return None
            
            processed_block = {
                "text": spell_checked_text,
                "original_text": original_text,
                "confidence": processed_confidence,
                "text_type": text_type,
                "bbox": block.get("bbox", {}),
                "polygon": block.get("polygon", []),
                "validation": validation_result,
                "processing_steps": {
                    "cleaned": cleaned_text,
                    "corrected": corrected_text,
                    "formatted": formatted_text,
                    "spell_checked": spell_checked_text
                }
            }
            
            return processed_block
            
        except Exception as e:
            logger.error(f"处理文本块失败: {e}")
            return None
    
    def _process_table(self, table: Dict) -> Optional[Dict]:
        """处理表格"""
        try:
            # 处理表格单元格
            processed_cells = []
            for cell in table.get("cells", []):
                processed_cell = self._process_text_block(cell)
                if processed_cell:
                    processed_cells.append(processed_cell)
            
            if not processed_cells:
                return None
            
            # 分析表格结构
            table_structure = self._analyze_table_content_structure(processed_cells)
            
            # 验证表格数据一致性
            consistency_check = self._check_table_consistency(processed_cells, table_structure)
            
            processed_table = {
                "html": table.get("html", ""),
                "cells": processed_cells,
                "bbox": table.get("bbox", {}),
                "confidence": table.get("confidence", 0.0),
                "structure": table_structure,
                "consistency": consistency_check,
                "cell_count": len(processed_cells)
            }
            
            return processed_table
            
        except Exception as e:
            logger.error(f"处理表格失败: {e}")
            return None
    
    def _clean_text(self, text: str) -> str:
        """清理文本"""
        try:
            # 移除多余的空白字符
            cleaned = re.sub(r'\s+', ' ', text.strip())
            
            # 移除特殊控制字符
            cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
            
            # 处理常见的OCR错误模式
            # 例如：将 "1 0 0 0" 修正为 "1000"
            cleaned = re.sub(r'(\d)\s+(\d)', r'\1\2', cleaned)
            
            # 修正货币符号后的空格
            cleaned = re.sub(r'([¥$€£])\s+(\d)', r'\1\2', cleaned)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清理文本失败: {e}")
            return text
    
    def _correct_characters(self, text: str) -> str:
        """字符校正"""
        try:
            corrected = text
            
            # 应用字符映射
            for wrong_char, correct_char in self.char_corrections.items():
                corrected = corrected.replace(wrong_char, correct_char)
            
            # 特殊情况处理
            # 修正常见的数字识别错误
            corrected = re.sub(r'[oO](?=\d)', '0', corrected)  # o/O -> 0 (在数字前)
            corrected = re.sub(r'(?<=\d)[oO]', '0', corrected)  # o/O -> 0 (在数字后)
            corrected = re.sub(r'[lI](?=\d)', '1', corrected)   # l/I -> 1 (在数字前)
            corrected = re.sub(r'(?<=\d)[lI]', '1', corrected)  # l/I -> 1 (在数字后)
            
            return corrected
            
        except Exception as e:
            logger.error(f"字符校正失败: {e}")
            return text
    
    def _format_text(self, text: str) -> Tuple[str, str]:
        """格式识别和标准化"""
        try:
            # 检测文本类型
            text_type = self._detect_text_type(text)
            
            formatted_text = text
            
            if text_type == "currency":
                # 货币格式标准化
                formatted_text = self._format_currency(text)
            elif text_type == "number":
                # 数字格式标准化
                formatted_text = self._format_number(text)
            elif text_type == "percentage":
                # 百分比格式标准化
                formatted_text = self._format_percentage(text)
            elif text_type == "date":
                # 日期格式标准化
                formatted_text = self._format_date(text)
            
            return formatted_text, text_type
            
        except Exception as e:
            logger.error(f"格式化文本失败: {e}")
            return text, "text"
    
    def _detect_text_type(self, text: str) -> str:
        """检测文本类型"""
        try:
            # 检查各种模式
            for pattern_type, pattern in self.number_patterns.items():
                if re.match(pattern, text.strip()):
                    return pattern_type
            
            # 检查是否包含金融术语
            if any(term in text for term in self.financial_terms):
                return "financial_term"
            
            # 检查是否为纯数字
            if text.replace(',', '').replace('.', '').replace(' ', '').isdigit():
                return "number"
            
            return "text"
            
        except Exception as e:
            logger.error(f"检测文本类型失败: {e}")
            return "text"
    
    def _format_currency(self, text: str) -> str:
        """格式化货币"""
        try:
            # 提取数字部分
            numbers = re.findall(r'\d+\.?\d*', text)
            if numbers:
                # 取最大的数字作为金额
                amount = max(numbers, key=lambda x: float(x))
                
                # 检测货币符号
                currency_symbol = "¥"  # 默认人民币
                if "$" in text:
                    currency_symbol = "$"
                elif "€" in text:
                    currency_symbol = "€"
                elif "£" in text:
                    currency_symbol = "£"
                
                # 格式化为标准货币格式
                try:
                    amount_float = float(amount)
                    if amount_float >= 1000:
                        formatted = f"{currency_symbol}{amount_float:,.2f}"
                    else:
                        formatted = f"{currency_symbol}{amount_float:.2f}"
                    return formatted
                except ValueError:
                    return text
            
            return text
            
        except Exception as e:
            logger.error(f"格式化货币失败: {e}")
            return text
    
    def _format_number(self, text: str) -> str:
        """格式化数字"""
        try:
            # 移除空格和逗号
            cleaned = text.replace(' ', '').replace(',', '')
            
            # 尝试转换为数字
            try:
                if '.' in cleaned:
                    num = float(cleaned)
                    # 保留合理的小数位数
                    if num == int(num):
                        return str(int(num))
                    else:
                        return f"{num:.2f}".rstrip('0').rstrip('.')
                else:
                    num = int(cleaned)
                    return str(num)
            except ValueError:
                return text
                
        except Exception as e:
            logger.error(f"格式化数字失败: {e}")
            return text
    
    def _format_percentage(self, text: str) -> str:
        """格式化百分比"""
        try:
            # 提取数字部分
            number_match = re.search(r'(\d+\.?\d*)', text)
            if number_match:
                number = float(number_match.group(1))
                return f"{number:.1f}%"
            return text
            
        except Exception as e:
            logger.error(f"格式化百分比失败: {e}")
            return text
    
    def _format_date(self, text: str) -> str:
        """格式化日期"""
        try:
            # 标准化日期分隔符
            formatted = re.sub(r'[/\\]', '-', text)
            
            # 确保年月日格式
            date_match = re.match(r'(\d{4})-(\d{1,2})-(\d{1,2})', formatted)
            if date_match:
                year, month, day = date_match.groups()
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
            
            return text
            
        except Exception as e:
            logger.error(f"格式化日期失败: {e}")
            return text
    
    def _spell_check(self, text: str) -> str:
        """拼写检查（简化版）"""
        try:
            # 这里可以集成更复杂的拼写检查库
            # 目前只做基本的常见错误修正
            
            corrections = {
                "teh": "the",
                "adn": "and",
                "taht": "that",
                "thsi": "this",
                "whcih": "which",
                "recieve": "receive",
                "seperate": "separate",
                "definately": "definitely"
            }
            
            words = text.split()
            corrected_words = []
            
            for word in words:
                # 检查是否需要修正
                lower_word = word.lower()
                if lower_word in corrections:
                    # 保持原始大小写格式
                    if word.isupper():
                        corrected_words.append(corrections[lower_word].upper())
                    elif word.istitle():
                        corrected_words.append(corrections[lower_word].title())
                    else:
                        corrected_words.append(corrections[lower_word])
                else:
                    corrected_words.append(word)
            
            return ' '.join(corrected_words)
            
        except Exception as e:
            logger.error(f"拼写检查失败: {e}")
            return text
    
    def _calculate_processed_confidence(self, original_confidence: float, validation_result: Dict) -> float:
        """计算处理后的置信度"""
        try:
            # 基础置信度
            base_confidence = original_confidence
            
            # 根据验证结果调整置信度
            if validation_result.get("is_valid", False):
                # 验证通过，提升置信度
                confidence_boost = 0.1
                base_confidence = min(1.0, base_confidence + confidence_boost)
            else:
                # 验证失败，降低置信度
                confidence_penalty = 0.2
                base_confidence = max(0.0, base_confidence - confidence_penalty)
            
            # 根据文本长度调整（过短或过长的文本可能不可靠）
            text_length = validation_result.get("text_length", 0)
            if text_length < 2:
                base_confidence *= 0.8
            elif text_length > 100:
                base_confidence *= 0.9
            
            return base_confidence
            
        except Exception as e:
            logger.error(f"计算处理后置信度失败: {e}")
            return original_confidence
    
    def _calculate_overall_confidence(self, text_blocks: List[Dict], tables: List[Dict]) -> float:
        """计算整体置信度"""
        try:
            all_confidences = []
            
            # 收集文本块置信度
            for block in text_blocks:
                all_confidences.append(block.get("confidence", 0.0))
            
            # 收集表格置信度
            for table in tables:
                all_confidences.append(table.get("confidence", 0.0))
            
            if all_confidences:
                return sum(all_confidences) / len(all_confidences)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"计算整体置信度失败: {e}")
            return 0.0
    
    def _generate_full_text(self, text_blocks: List[Dict], tables: List[Dict]) -> str:
        """生成完整文本"""
        try:
            text_parts = []
            
            # 添加文本块
            for block in text_blocks:
                text_parts.append(block.get("text", ""))
            
            # 添加表格内容
            for table in tables:
                table_text = []
                for cell in table.get("cells", []):
                    cell_text = cell.get("text", "").strip()
                    if cell_text:
                        table_text.append(cell_text)
                
                if table_text:
                    text_parts.append(" | ".join(table_text))
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.error(f"生成完整文本失败: {e}")
            return ""
    
    def _analyze_table_content_structure(self, cells: List[Dict]) -> Dict:
        """分析表格内容结构"""
        try:
            # 统计不同类型的单元格
            type_counts = {}
            for cell in cells:
                cell_type = cell.get("text_type", "text")
                type_counts[cell_type] = type_counts.get(cell_type, 0) + 1
            
            # 检测是否为财务表格
            is_financial = any(cell_type in ["currency", "number", "percentage"] 
                             for cell_type in type_counts.keys())
            
            return {
                "cell_types": type_counts,
                "is_financial": is_financial,
                "total_cells": len(cells),
                "dominant_type": max(type_counts.items(), key=lambda x: x[1])[0] if type_counts else "text"
            }
            
        except Exception as e:
            logger.error(f"分析表格内容结构失败: {e}")
            return {}
    
    def _check_table_consistency(self, cells: List[Dict], structure: Dict) -> Dict:
        """检查表格数据一致性"""
        try:
            consistency_issues = []
            
            # 检查数字格式一致性
            if structure.get("is_financial", False):
                currency_formats = set()
                for cell in cells:
                    if cell.get("text_type") == "currency":
                        # 提取货币符号
                        text = cell.get("text", "")
                        if text.startswith(("¥", "$", "€", "£")):
                            currency_formats.add(text[0])
                
                if len(currency_formats) > 1:
                    consistency_issues.append("混合货币符号")
            
            # 检查空单元格比例
            empty_cells = sum(1 for cell in cells if not cell.get("text", "").strip())
            empty_ratio = empty_cells / len(cells) if cells else 0
            
            if empty_ratio > 0.3:
                consistency_issues.append("空单元格过多")
            
            return {
                "issues": consistency_issues,
                "empty_cell_ratio": empty_ratio,
                "is_consistent": len(consistency_issues) == 0
            }
            
        except Exception as e:
            logger.error(f"检查表格一致性失败: {e}")
            return {"issues": [], "is_consistent": True}
