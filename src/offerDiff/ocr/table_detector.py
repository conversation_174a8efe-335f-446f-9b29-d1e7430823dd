"""
表格检测器
"""
from typing import List, Dict, Tuple, Optional
import numpy as np
import cv2
from loguru import logger

from src.offerDiff.utils.image_utils import load_image, convert_to_grayscale


class TableDetector:
    """表格检测器"""
    
    def __init__(self):
        self.min_table_area = 1000  # 最小表格面积
        self.min_rows = 2  # 最小行数
        self.min_cols = 2  # 最小列数
        self.line_thickness_threshold = 3  # 线条粗细阈值
    
    def detect_tables(self, image_path: str) -> List[Dict]:
        """
        检测图像中的表格
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            表格检测结果列表
        """
        try:
            # 加载图像
            image = load_image(image_path)
            gray = convert_to_grayscale(image)
            
            # 方法1：基于线条检测的表格识别
            tables_lines = self._detect_tables_by_lines(gray)
            
            # 方法2：基于轮廓检测的表格识别
            tables_contours = self._detect_tables_by_contours(gray)
            
            # 方法3：基于形态学操作的表格识别
            tables_morphology = self._detect_tables_by_morphology(gray)
            
            # 合并和去重结果
            all_tables = tables_lines + tables_contours + tables_morphology
            merged_tables = self._merge_overlapping_tables(all_tables)
            
            # 验证和过滤表格
            valid_tables = self._validate_tables(merged_tables, image.shape)
            
            logger.info(f"表格检测完成: {image_path}, 检测到 {len(valid_tables)} 个表格")
            return valid_tables
            
        except Exception as e:
            logger.error(f"表格检测失败: {e}")
            return []
    
    def _detect_tables_by_lines(self, gray_image: np.ndarray) -> List[Dict]:
        """基于线条检测表格"""
        try:
            # 边缘检测
            edges = cv2.Canny(gray_image, 50, 150, apertureSize=3)
            
            # 检测水平线
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (25, 1))
            horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)
            
            # 检测垂直线
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 25))
            vertical_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, vertical_kernel)
            
            # 合并水平线和垂直线
            table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
            
            # 膨胀操作连接线条
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            table_mask = cv2.dilate(table_mask, kernel, iterations=2)
            
            # 查找轮廓
            contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            tables = []
            for contour in contours:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 过滤小区域
                if area > self.min_table_area:
                    table = {
                        "method": "lines",
                        "bbox": {"x1": x, "y1": y, "x2": x + w, "y2": y + h},
                        "area": area,
                        "confidence": 0.8
                    }
                    tables.append(table)
            
            return tables
            
        except Exception as e:
            logger.error(f"基于线条的表格检测失败: {e}")
            return []
    
    def _detect_tables_by_contours(self, gray_image: np.ndarray) -> List[Dict]:
        """基于轮廓检测表格"""
        try:
            # 二值化
            _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            tables = []
            for contour in contours:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 计算轮廓特征
                aspect_ratio = w / h if h > 0 else 0
                extent = area / (w * h) if w * h > 0 else 0
                
                # 表格特征判断
                if (area > self.min_table_area and 
                    0.3 < aspect_ratio < 5.0 and 
                    extent > 0.5):
                    
                    table = {
                        "method": "contours",
                        "bbox": {"x1": x, "y1": y, "x2": x + w, "y2": y + h},
                        "area": area,
                        "aspect_ratio": aspect_ratio,
                        "extent": extent,
                        "confidence": 0.7
                    }
                    tables.append(table)
            
            return tables
            
        except Exception as e:
            logger.error(f"基于轮廓的表格检测失败: {e}")
            return []
    
    def _detect_tables_by_morphology(self, gray_image: np.ndarray) -> List[Dict]:
        """基于形态学操作检测表格"""
        try:
            # 二值化
            _, binary = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 检测水平结构
            horizontal_size = gray_image.shape[1] // 30
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (horizontal_size, 1))
            horizontal = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
            
            # 检测垂直结构
            vertical_size = gray_image.shape[0] // 30
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, vertical_size))
            vertical = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
            
            # 合并水平和垂直结构
            table_structure = cv2.addWeighted(horizontal, 0.5, vertical, 0.5, 0.0)
            
            # 膨胀操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            table_structure = cv2.dilate(table_structure, kernel, iterations=3)
            
            # 查找轮廓
            contours, _ = cv2.findContours(table_structure, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            tables = []
            for contour in contours:
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 过滤条件
                if area > self.min_table_area:
                    table = {
                        "method": "morphology",
                        "bbox": {"x1": x, "y1": y, "x2": x + w, "y2": y + h},
                        "area": area,
                        "confidence": 0.6
                    }
                    tables.append(table)
            
            return tables
            
        except Exception as e:
            logger.error(f"基于形态学的表格检测失败: {e}")
            return []
    
    def _merge_overlapping_tables(self, tables: List[Dict]) -> List[Dict]:
        """合并重叠的表格"""
        if not tables:
            return []
        
        try:
            # 按面积排序（大的优先）
            tables.sort(key=lambda x: x["area"], reverse=True)
            
            merged = []
            for table in tables:
                bbox1 = table["bbox"]
                
                # 检查是否与已有表格重叠
                overlapped = False
                for existing in merged:
                    bbox2 = existing["bbox"]
                    
                    # 计算重叠面积
                    overlap_area = self._calculate_overlap_area(bbox1, bbox2)
                    min_area = min(table["area"], existing["area"])
                    
                    # 如果重叠面积超过较小表格的50%，认为是重叠
                    if overlap_area > min_area * 0.5:
                        overlapped = True
                        
                        # 合并边界框
                        existing["bbox"] = self._merge_bboxes(bbox1, bbox2)
                        existing["area"] = self._calculate_bbox_area(existing["bbox"])
                        existing["confidence"] = max(existing["confidence"], table["confidence"])
                        break
                
                if not overlapped:
                    merged.append(table)
            
            return merged
            
        except Exception as e:
            logger.error(f"合并重叠表格失败: {e}")
            return tables
    
    def _calculate_overlap_area(self, bbox1: Dict, bbox2: Dict) -> int:
        """计算两个边界框的重叠面积"""
        x1 = max(bbox1["x1"], bbox2["x1"])
        y1 = max(bbox1["y1"], bbox2["y1"])
        x2 = min(bbox1["x2"], bbox2["x2"])
        y2 = min(bbox1["y2"], bbox2["y2"])
        
        if x1 < x2 and y1 < y2:
            return (x2 - x1) * (y2 - y1)
        return 0
    
    def _merge_bboxes(self, bbox1: Dict, bbox2: Dict) -> Dict:
        """合并两个边界框"""
        return {
            "x1": min(bbox1["x1"], bbox2["x1"]),
            "y1": min(bbox1["y1"], bbox2["y1"]),
            "x2": max(bbox1["x2"], bbox2["x2"]),
            "y2": max(bbox1["y2"], bbox2["y2"])
        }
    
    def _calculate_bbox_area(self, bbox: Dict) -> int:
        """计算边界框面积"""
        return (bbox["x2"] - bbox["x1"]) * (bbox["y2"] - bbox["y1"])
    
    def _validate_tables(self, tables: List[Dict], image_shape: Tuple) -> List[Dict]:
        """验证和过滤表格"""
        valid_tables = []
        
        for table in tables:
            bbox = table["bbox"]
            
            # 检查边界框是否在图像范围内
            if (bbox["x1"] >= 0 and bbox["y1"] >= 0 and 
                bbox["x2"] <= image_shape[1] and bbox["y2"] <= image_shape[0]):
                
                # 检查尺寸是否合理
                width = bbox["x2"] - bbox["x1"]
                height = bbox["y2"] - bbox["y1"]
                
                if (width >= 100 and height >= 50 and  # 最小尺寸
                    width <= image_shape[1] * 0.9 and height <= image_shape[0] * 0.9):  # 最大尺寸
                    
                    valid_tables.append(table)
        
        return valid_tables
    
    def analyze_table_structure(self, image: np.ndarray, table_bbox: Dict) -> Dict:
        """分析表格结构"""
        try:
            # 裁剪表格区域
            x1, y1, x2, y2 = table_bbox["x1"], table_bbox["y1"], table_bbox["x2"], table_bbox["y2"]
            table_region = image[y1:y2, x1:x2]
            
            if len(table_region.shape) == 3:
                table_gray = cv2.cvtColor(table_region, cv2.COLOR_RGB2GRAY)
            else:
                table_gray = table_region
            
            # 检测行和列
            rows = self._detect_table_rows(table_gray)
            cols = self._detect_table_columns(table_gray)
            
            # 分析表格属性
            structure = {
                "rows": len(rows),
                "columns": len(cols),
                "row_positions": rows,
                "column_positions": cols,
                "has_header": self._detect_header(table_gray, rows),
                "has_borders": self._detect_borders(table_gray),
                "cell_count": len(rows) * len(cols) if rows and cols else 0
            }
            
            return structure
            
        except Exception as e:
            logger.error(f"分析表格结构失败: {e}")
            return {"rows": 0, "columns": 0, "error": str(e)}
    
    def _detect_table_rows(self, table_image: np.ndarray) -> List[int]:
        """检测表格行"""
        try:
            # 水平投影
            horizontal_projection = np.sum(table_image < 128, axis=1)
            
            # 找到投影的峰值（行分隔线）
            rows = []
            threshold = np.mean(horizontal_projection) * 0.5
            
            for i, value in enumerate(horizontal_projection):
                if value > threshold:
                    rows.append(i)
            
            # 合并相邻的行位置
            merged_rows = []
            if rows:
                current_row = rows[0]
                for row in rows[1:]:
                    if row - current_row > 5:  # 间隔大于5像素认为是新行
                        merged_rows.append(current_row)
                        current_row = row
                merged_rows.append(current_row)
            
            return merged_rows
            
        except Exception as e:
            logger.error(f"检测表格行失败: {e}")
            return []
    
    def _detect_table_columns(self, table_image: np.ndarray) -> List[int]:
        """检测表格列"""
        try:
            # 垂直投影
            vertical_projection = np.sum(table_image < 128, axis=0)
            
            # 找到投影的峰值（列分隔线）
            cols = []
            threshold = np.mean(vertical_projection) * 0.5
            
            for i, value in enumerate(vertical_projection):
                if value > threshold:
                    cols.append(i)
            
            # 合并相邻的列位置
            merged_cols = []
            if cols:
                current_col = cols[0]
                for col in cols[1:]:
                    if col - current_col > 5:  # 间隔大于5像素认为是新列
                        merged_cols.append(current_col)
                        current_col = col
                merged_cols.append(current_col)
            
            return merged_cols
            
        except Exception as e:
            logger.error(f"检测表格列失败: {e}")
            return []
    
    def _detect_header(self, table_image: np.ndarray, rows: List[int]) -> bool:
        """检测是否有表头"""
        try:
            if len(rows) < 2:
                return False
            
            # 检查第一行是否与其他行有明显差异
            first_row_end = rows[1] if len(rows) > 1 else table_image.shape[0] // 2
            first_row = table_image[0:first_row_end, :]
            
            # 计算第一行的特征
            first_row_mean = np.mean(first_row)
            first_row_std = np.std(first_row)
            
            # 计算其他行的特征
            if len(rows) > 2:
                other_rows = table_image[rows[1]:, :]
                other_rows_mean = np.mean(other_rows)
                
                # 如果第一行明显更暗（可能是表头），返回True
                return first_row_mean < other_rows_mean * 0.9
            
            return False
            
        except Exception as e:
            logger.error(f"检测表头失败: {e}")
            return False
    
    def _detect_borders(self, table_image: np.ndarray) -> bool:
        """检测是否有边框"""
        try:
            # 检测边缘
            edges = cv2.Canny(table_image, 50, 150)
            
            # 计算边缘像素比例
            edge_ratio = np.sum(edges > 0) / edges.size
            
            # 如果边缘像素比例超过阈值，认为有边框
            return edge_ratio > 0.05
            
        except Exception as e:
            logger.error(f"检测边框失败: {e}")
            return False
