"""
PaddleOCR引擎封装
"""
import os
from typing import List, Dict, Tuple, Optional, Any
import numpy as np
from loguru import logger

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    logger.warning("PaddleOCR未安装，OCR功能将不可用")
    PADDLEOCR_AVAILABLE = False

from config.settings import settings
from src.offerDiff.utils.image_utils import load_image


class PaddleOCREngine:
    """PaddleOCR引擎封装类"""
    
    def __init__(self):
        if not PADDLEOCR_AVAILABLE:
            raise ImportError("PaddleOCR未安装，请先安装: pip install paddlepaddle paddleocr")
        
        self.ocr_engine = None
        self.table_engine = None
        self._initialize_engines()
    
    def _initialize_engines(self):
        """初始化OCR引擎"""
        try:
            # 初始化文本OCR引擎
            self.ocr_engine = PaddleOCR(
                use_angle_cls=True,  # 使用角度分类器
                lang=settings.ocr_languages,  # 语言设置
                use_gpu=settings.use_gpu,  # GPU设置
                show_log=False,  # 不显示日志
                det_model_dir=None,  # 使用默认检测模型
                rec_model_dir=None,  # 使用默认识别模型
                cls_model_dir=None,  # 使用默认分类模型
            )
            
            # 初始化表格OCR引擎
            try:
                from paddleocr import PPStructure
                self.table_engine = PPStructure(
                    table=True,
                    ocr=True,
                    show_log=False,
                    lang=settings.ocr_languages,
                    use_gpu=settings.use_gpu
                )
                logger.info("表格OCR引擎初始化成功")
            except ImportError:
                logger.warning("PPStructure未安装，表格识别功能将不可用")
                self.table_engine = None
            
            logger.info("PaddleOCR引擎初始化成功")
            
        except Exception as e:
            logger.error(f"PaddleOCR引擎初始化失败: {e}")
            raise
    
    def extract_text(self, image_path: str) -> Dict:
        """
        提取图像中的文本
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            文本提取结果
        """
        try:
            if not self.ocr_engine:
                raise RuntimeError("OCR引擎未初始化")
            
            # 加载图像
            image = load_image(image_path)
            
            # 执行OCR识别
            results = self.ocr_engine.ocr(image, cls=True)
            
            # 处理结果
            extracted_data = self._process_ocr_results(results)
            
            logger.info(f"文本提取完成: {image_path}, 识别到 {len(extracted_data['text_blocks'])} 个文本块")
            return extracted_data
            
        except Exception as e:
            logger.error(f"文本提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text_blocks": [],
                "full_text": "",
                "confidence": 0.0
            }
    
    def extract_table(self, image_path: str) -> Dict:
        """
        提取图像中的表格
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            表格提取结果
        """
        try:
            if not self.table_engine:
                logger.warning("表格OCR引擎不可用，使用普通OCR")
                return self._extract_table_fallback(image_path)
            
            # 加载图像
            image = load_image(image_path)
            
            # 执行表格识别
            results = self.table_engine(image)
            
            # 处理表格结果
            table_data = self._process_table_results(results)
            
            logger.info(f"表格提取完成: {image_path}, 识别到 {len(table_data['tables'])} 个表格")
            return table_data
            
        except Exception as e:
            logger.error(f"表格提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tables": [],
                "confidence": 0.0
            }
    
    def extract_mixed_content(self, image_path: str) -> Dict:
        """
        提取图像中的混合内容（文本+表格）
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            混合内容提取结果
        """
        try:
            # 提取文本
            text_result = self.extract_text(image_path)
            
            # 提取表格
            table_result = self.extract_table(image_path)
            
            # 合并结果
            mixed_result = {
                "success": text_result.get("success", False) and table_result.get("success", False),
                "image_path": image_path,
                "text_blocks": text_result.get("text_blocks", []),
                "tables": table_result.get("tables", []),
                "full_text": text_result.get("full_text", ""),
                "text_confidence": text_result.get("confidence", 0.0),
                "table_confidence": table_result.get("confidence", 0.0),
                "overall_confidence": (text_result.get("confidence", 0.0) + table_result.get("confidence", 0.0)) / 2
            }
            
            return mixed_result
            
        except Exception as e:
            logger.error(f"混合内容提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text_blocks": [],
                "tables": [],
                "full_text": "",
                "overall_confidence": 0.0
            }
    
    def _process_ocr_results(self, results: List) -> Dict:
        """处理OCR识别结果"""
        try:
            text_blocks = []
            full_text_parts = []
            total_confidence = 0.0
            valid_blocks = 0
            
            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        # 提取坐标信息
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # 文本和置信度
                        
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                        else:
                            text = str(text_info)
                            confidence = 0.8  # 默认置信度
                        
                        # 计算边界框
                        x_coords = [point[0] for point in bbox]
                        y_coords = [point[1] for point in bbox]
                        
                        text_block = {
                            "text": text,
                            "confidence": float(confidence),
                            "bbox": {
                                "x1": int(min(x_coords)),
                                "y1": int(min(y_coords)),
                                "x2": int(max(x_coords)),
                                "y2": int(max(y_coords))
                            },
                            "polygon": [[int(point[0]), int(point[1])] for point in bbox]
                        }
                        
                        text_blocks.append(text_block)
                        full_text_parts.append(text)
                        total_confidence += confidence
                        valid_blocks += 1
            
            # 计算平均置信度
            avg_confidence = total_confidence / valid_blocks if valid_blocks > 0 else 0.0
            
            return {
                "success": True,
                "text_blocks": text_blocks,
                "full_text": "\n".join(full_text_parts),
                "confidence": avg_confidence,
                "total_blocks": len(text_blocks)
            }
            
        except Exception as e:
            logger.error(f"处理OCR结果失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "text_blocks": [],
                "full_text": "",
                "confidence": 0.0
            }
    
    def _process_table_results(self, results: List) -> Dict:
        """处理表格识别结果"""
        try:
            tables = []
            total_confidence = 0.0
            valid_tables = 0
            
            for result in results:
                if result.get('type') == 'table':
                    # 提取表格HTML
                    table_html = result.get('res', {}).get('html', '')
                    
                    # 提取表格边界框
                    bbox = result.get('bbox', [0, 0, 0, 0])
                    
                    # 提取表格单元格
                    cells = self._extract_table_cells(result)
                    
                    table_info = {
                        "html": table_html,
                        "cells": cells,
                        "bbox": {
                            "x1": int(bbox[0]),
                            "y1": int(bbox[1]),
                            "x2": int(bbox[2]),
                            "y2": int(bbox[3])
                        },
                        "confidence": 0.9  # 默认置信度
                    }
                    
                    tables.append(table_info)
                    total_confidence += 0.9
                    valid_tables += 1
            
            # 计算平均置信度
            avg_confidence = total_confidence / valid_tables if valid_tables > 0 else 0.0
            
            return {
                "success": True,
                "tables": tables,
                "confidence": avg_confidence,
                "total_tables": len(tables)
            }
            
        except Exception as e:
            logger.error(f"处理表格结果失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tables": [],
                "confidence": 0.0
            }
    
    def _extract_table_cells(self, table_result: Dict) -> List[Dict]:
        """从表格结果中提取单元格信息"""
        cells = []
        
        try:
            # 尝试从OCR结果中提取单元格
            ocr_result = table_result.get('res', {}).get('ocr_result', [])
            
            for cell_info in ocr_result:
                if len(cell_info) >= 2:
                    bbox = cell_info[0]
                    text_info = cell_info[1]
                    
                    if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                        text = text_info[0]
                        confidence = text_info[1]
                    else:
                        text = str(text_info)
                        confidence = 0.8
                    
                    # 计算边界框
                    x_coords = [point[0] for point in bbox]
                    y_coords = [point[1] for point in bbox]
                    
                    cell = {
                        "text": text,
                        "confidence": float(confidence),
                        "bbox": {
                            "x1": int(min(x_coords)),
                            "y1": int(min(y_coords)),
                            "x2": int(max(x_coords)),
                            "y2": int(max(y_coords))
                        }
                    }
                    
                    cells.append(cell)
            
        except Exception as e:
            logger.error(f"提取表格单元格失败: {e}")
        
        return cells
    
    def _extract_table_fallback(self, image_path: str) -> Dict:
        """表格提取的备用方法（使用普通OCR）"""
        try:
            # 使用普通OCR提取文本
            text_result = self.extract_text(image_path)
            
            if not text_result.get("success", False):
                return text_result
            
            # 尝试从文本块中识别表格结构
            text_blocks = text_result.get("text_blocks", [])
            
            # 简单的表格检测：查找对齐的文本块
            tables = self._detect_table_from_text_blocks(text_blocks)
            
            return {
                "success": True,
                "tables": tables,
                "confidence": text_result.get("confidence", 0.0) * 0.8  # 降低置信度
            }
            
        except Exception as e:
            logger.error(f"备用表格提取失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "tables": [],
                "confidence": 0.0
            }
    
    def _detect_table_from_text_blocks(self, text_blocks: List[Dict]) -> List[Dict]:
        """从文本块中检测表格结构"""
        tables = []
        
        try:
            # 按Y坐标排序文本块
            sorted_blocks = sorted(text_blocks, key=lambda x: x["bbox"]["y1"])
            
            # 简单的行检测：Y坐标相近的文本块归为一行
            rows = []
            current_row = []
            current_y = None
            y_threshold = 10  # Y坐标差异阈值
            
            for block in sorted_blocks:
                block_y = block["bbox"]["y1"]
                
                if current_y is None or abs(block_y - current_y) <= y_threshold:
                    current_row.append(block)
                    current_y = block_y
                else:
                    if current_row:
                        rows.append(sorted(current_row, key=lambda x: x["bbox"]["x1"]))
                    current_row = [block]
                    current_y = block_y
            
            if current_row:
                rows.append(sorted(current_row, key=lambda x: x["bbox"]["x1"]))
            
            # 如果有多行且每行有多个单元格，认为是表格
            if len(rows) >= 2 and all(len(row) >= 2 for row in rows):
                # 计算表格边界
                all_blocks = [block for row in rows for block in row]
                x_coords = [block["bbox"]["x1"] for block in all_blocks] + [block["bbox"]["x2"] for block in all_blocks]
                y_coords = [block["bbox"]["y1"] for block in all_blocks] + [block["bbox"]["y2"] for block in all_blocks]
                
                table = {
                    "html": "",  # 无法生成HTML
                    "cells": all_blocks,
                    "rows": rows,
                    "bbox": {
                        "x1": min(x_coords),
                        "y1": min(y_coords),
                        "x2": max(x_coords),
                        "y2": max(y_coords)
                    },
                    "confidence": 0.7  # 较低的置信度
                }
                
                tables.append(table)
            
        except Exception as e:
            logger.error(f"从文本块检测表格失败: {e}")
        
        return tables
    
    def get_engine_info(self) -> Dict:
        """获取引擎信息"""
        return {
            "engine_type": "PaddleOCR",
            "version": "2.7.0+",
            "languages": settings.ocr_languages,
            "use_gpu": settings.use_gpu,
            "text_ocr_available": self.ocr_engine is not None,
            "table_ocr_available": self.table_engine is not None
        }
