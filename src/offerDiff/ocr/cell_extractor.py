"""
表格单元格提取器
"""
from typing import List, Dict, Tuple, Optional
import numpy as np
import cv2
from loguru import logger

from src.offerDiff.utils.image_utils import load_image, convert_to_grayscale


class CellExtractor:
    """表格单元格提取器"""
    
    def __init__(self):
        self.min_cell_area = 50  # 最小单元格面积
        self.merge_threshold = 0.8  # 单元格合并阈值
    
    def extract_cells(self, image: np.ndarray, table_bbox: Dict, table_structure: Dict) -> List[Dict]:
        """
        提取表格单元格
        
        Args:
            image: 输入图像
            table_bbox: 表格边界框
            table_structure: 表格结构信息
            
        Returns:
            单元格列表
        """
        try:
            # 裁剪表格区域
            x1, y1, x2, y2 = table_bbox["x1"], table_bbox["y1"], table_bbox["x2"], table_bbox["y2"]
            table_region = image[y1:y2, x1:x2]
            
            # 转换为灰度图像
            if len(table_region.shape) == 3:
                table_gray = cv2.cvtColor(table_region, cv2.COLOR_RGB2GRAY)
            else:
                table_gray = table_region
            
            # 获取行列位置
            rows = table_structure.get("row_positions", [])
            cols = table_structure.get("column_positions", [])
            
            if not rows or not cols:
                # 如果没有检测到行列，尝试自动分割
                return self._auto_extract_cells(table_gray, table_bbox)
            
            # 基于行列位置提取单元格
            cells = self._extract_cells_by_grid(table_gray, rows, cols, table_bbox)
            
            # 检测合并单元格
            cells = self._detect_merged_cells(cells, table_gray)
            
            # 过滤和验证单元格
            valid_cells = self._validate_cells(cells)
            
            logger.info(f"单元格提取完成，共提取 {len(valid_cells)} 个单元格")
            return valid_cells
            
        except Exception as e:
            logger.error(f"单元格提取失败: {e}")
            return []
    
    def _extract_cells_by_grid(self, table_image: np.ndarray, rows: List[int], cols: List[int], table_bbox: Dict) -> List[Dict]:
        """基于网格提取单元格"""
        cells = []
        
        try:
            # 添加边界
            all_rows = [0] + rows + [table_image.shape[0]]
            all_cols = [0] + cols + [table_image.shape[1]]
            
            # 去重并排序
            all_rows = sorted(list(set(all_rows)))
            all_cols = sorted(list(set(all_cols)))
            
            # 提取每个单元格
            for i in range(len(all_rows) - 1):
                for j in range(len(all_cols) - 1):
                    # 计算单元格边界
                    cell_y1 = all_rows[i]
                    cell_y2 = all_rows[i + 1]
                    cell_x1 = all_cols[j]
                    cell_x2 = all_cols[j + 1]
                    
                    # 检查单元格尺寸
                    cell_width = cell_x2 - cell_x1
                    cell_height = cell_y2 - cell_y1
                    cell_area = cell_width * cell_height
                    
                    if cell_area >= self.min_cell_area:
                        # 转换为全局坐标
                        global_x1 = table_bbox["x1"] + cell_x1
                        global_y1 = table_bbox["y1"] + cell_y1
                        global_x2 = table_bbox["x1"] + cell_x2
                        global_y2 = table_bbox["y1"] + cell_y2
                        
                        cell = {
                            "row": i,
                            "col": j,
                            "bbox": {
                                "x1": global_x1,
                                "y1": global_y1,
                                "x2": global_x2,
                                "y2": global_y2
                            },
                            "local_bbox": {
                                "x1": cell_x1,
                                "y1": cell_y1,
                                "x2": cell_x2,
                                "y2": cell_y2
                            },
                            "width": cell_width,
                            "height": cell_height,
                            "area": cell_area,
                            "is_merged": False,
                            "merged_cells": [],
                            "text": "",
                            "confidence": 0.0
                        }
                        
                        cells.append(cell)
            
            return cells
            
        except Exception as e:
            logger.error(f"基于网格提取单元格失败: {e}")
            return []
    
    def _auto_extract_cells(self, table_image: np.ndarray, table_bbox: Dict) -> List[Dict]:
        """自动提取单元格（当无法检测到明确的行列时）"""
        try:
            # 使用轮廓检测方法
            cells = self._extract_cells_by_contours(table_image, table_bbox)
            
            if not cells:
                # 使用分水岭算法
                cells = self._extract_cells_by_watershed(table_image, table_bbox)
            
            return cells
            
        except Exception as e:
            logger.error(f"自动单元格提取失败: {e}")
            return []
    
    def _extract_cells_by_contours(self, table_image: np.ndarray, table_bbox: Dict) -> List[Dict]:
        """基于轮廓提取单元格"""
        try:
            # 二值化
            _, binary = cv2.threshold(table_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            
            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            # 查找轮廓
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            cells = []
            for i, contour in enumerate(contours):
                # 计算边界框
                x, y, w, h = cv2.boundingRect(contour)
                area = w * h
                
                # 过滤小区域
                if area >= self.min_cell_area:
                    # 转换为全局坐标
                    global_x1 = table_bbox["x1"] + x
                    global_y1 = table_bbox["y1"] + y
                    global_x2 = table_bbox["x1"] + x + w
                    global_y2 = table_bbox["y1"] + y + h
                    
                    cell = {
                        "row": -1,  # 未知行
                        "col": -1,  # 未知列
                        "bbox": {
                            "x1": global_x1,
                            "y1": global_y1,
                            "x2": global_x2,
                            "y2": global_y2
                        },
                        "local_bbox": {
                            "x1": x,
                            "y1": y,
                            "x2": x + w,
                            "y2": y + h
                        },
                        "width": w,
                        "height": h,
                        "area": area,
                        "is_merged": False,
                        "merged_cells": [],
                        "text": "",
                        "confidence": 0.0
                    }
                    
                    cells.append(cell)
            
            # 尝试推断行列位置
            cells = self._infer_grid_positions(cells)
            
            return cells
            
        except Exception as e:
            logger.error(f"基于轮廓提取单元格失败: {e}")
            return []
    
    def _extract_cells_by_watershed(self, table_image: np.ndarray, table_bbox: Dict) -> List[Dict]:
        """基于分水岭算法提取单元格"""
        try:
            # 距离变换
            _, binary = cv2.threshold(table_image, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
            dist_transform = cv2.distanceTransform(binary, cv2.DIST_L2, 5)
            
            # 查找局部最大值
            _, sure_fg = cv2.threshold(dist_transform, 0.7 * dist_transform.max(), 255, 0)
            sure_fg = np.uint8(sure_fg)
            
            # 查找连通组件
            _, markers = cv2.connectedComponents(sure_fg)
            
            # 分水岭算法
            markers = cv2.watershed(cv2.cvtColor(table_image, cv2.COLOR_GRAY2BGR), markers)
            
            # 提取单元格
            cells = []
            unique_labels = np.unique(markers)
            
            for label in unique_labels:
                if label <= 1:  # 跳过背景和边界
                    continue
                
                # 创建掩码
                mask = (markers == label).astype(np.uint8) * 255
                
                # 查找轮廓
                contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                if contours:
                    # 取最大轮廓
                    contour = max(contours, key=cv2.contourArea)
                    x, y, w, h = cv2.boundingRect(contour)
                    area = w * h
                    
                    if area >= self.min_cell_area:
                        # 转换为全局坐标
                        global_x1 = table_bbox["x1"] + x
                        global_y1 = table_bbox["y1"] + y
                        global_x2 = table_bbox["x1"] + x + w
                        global_y2 = table_bbox["y1"] + y + h
                        
                        cell = {
                            "row": -1,
                            "col": -1,
                            "bbox": {
                                "x1": global_x1,
                                "y1": global_y1,
                                "x2": global_x2,
                                "y2": global_y2
                            },
                            "local_bbox": {
                                "x1": x,
                                "y1": y,
                                "x2": x + w,
                                "y2": y + h
                            },
                            "width": w,
                            "height": h,
                            "area": area,
                            "is_merged": False,
                            "merged_cells": [],
                            "text": "",
                            "confidence": 0.0
                        }
                        
                        cells.append(cell)
            
            return cells
            
        except Exception as e:
            logger.error(f"基于分水岭算法提取单元格失败: {e}")
            return []
    
    def _detect_merged_cells(self, cells: List[Dict], table_image: np.ndarray) -> List[Dict]:
        """检测合并单元格"""
        try:
            # 按行列排序
            cells.sort(key=lambda x: (x["row"], x["col"]))
            
            merged_cells = []
            processed = set()
            
            for i, cell in enumerate(cells):
                if i in processed:
                    continue
                
                # 检查是否为合并单元格
                merge_candidates = []
                
                for j, other_cell in enumerate(cells[i+1:], i+1):
                    if j in processed:
                        continue
                    
                    # 检查是否相邻且可能合并
                    if self._can_merge_cells(cell, other_cell, table_image):
                        merge_candidates.append(j)
                
                if merge_candidates:
                    # 创建合并单元格
                    merged_cell = self._create_merged_cell(cell, [cells[j] for j in merge_candidates])
                    merged_cells.append(merged_cell)
                    
                    # 标记为已处理
                    processed.add(i)
                    processed.update(merge_candidates)
                else:
                    merged_cells.append(cell)
                    processed.add(i)
            
            return merged_cells
            
        except Exception as e:
            logger.error(f"检测合并单元格失败: {e}")
            return cells
    
    def _can_merge_cells(self, cell1: Dict, cell2: Dict, table_image: np.ndarray) -> bool:
        """判断两个单元格是否可以合并"""
        try:
            bbox1 = cell1["local_bbox"]
            bbox2 = cell2["local_bbox"]
            
            # 检查是否相邻
            adjacent = (
                # 水平相邻
                (abs(bbox1["x2"] - bbox2["x1"]) <= 5 and 
                 bbox1["y1"] <= bbox2["y2"] and bbox1["y2"] >= bbox2["y1"]) or
                # 垂直相邻
                (abs(bbox1["y2"] - bbox2["y1"]) <= 5 and 
                 bbox1["x1"] <= bbox2["x2"] and bbox1["x2"] >= bbox2["x1"])
            )
            
            if not adjacent:
                return False
            
            # 检查中间是否有分隔线
            # 计算两个单元格之间的区域
            min_x = min(bbox1["x1"], bbox2["x1"])
            max_x = max(bbox1["x2"], bbox2["x2"])
            min_y = min(bbox1["y1"], bbox2["y1"])
            max_y = max(bbox1["y2"], bbox2["y2"])
            
            # 提取中间区域
            middle_region = table_image[min_y:max_y, min_x:max_x]
            
            # 检测是否有明显的分隔线
            edges = cv2.Canny(middle_region, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            
            # 如果边缘密度低，可能是合并单元格
            return edge_density < 0.1
            
        except Exception as e:
            logger.error(f"判断单元格合并失败: {e}")
            return False
    
    def _create_merged_cell(self, main_cell: Dict, merge_candidates: List[Dict]) -> Dict:
        """创建合并单元格"""
        try:
            all_cells = [main_cell] + merge_candidates
            
            # 计算合并后的边界框
            min_x1 = min(cell["bbox"]["x1"] for cell in all_cells)
            min_y1 = min(cell["bbox"]["y1"] for cell in all_cells)
            max_x2 = max(cell["bbox"]["x2"] for cell in all_cells)
            max_y2 = max(cell["bbox"]["y2"] for cell in all_cells)
            
            # 计算本地边界框
            local_min_x1 = min(cell["local_bbox"]["x1"] for cell in all_cells)
            local_min_y1 = min(cell["local_bbox"]["y1"] for cell in all_cells)
            local_max_x2 = max(cell["local_bbox"]["x2"] for cell in all_cells)
            local_max_y2 = max(cell["local_bbox"]["y2"] for cell in all_cells)
            
            merged_cell = {
                "row": main_cell["row"],
                "col": main_cell["col"],
                "bbox": {
                    "x1": min_x1,
                    "y1": min_y1,
                    "x2": max_x2,
                    "y2": max_y2
                },
                "local_bbox": {
                    "x1": local_min_x1,
                    "y1": local_min_y1,
                    "x2": local_max_x2,
                    "y2": local_max_y2
                },
                "width": max_x2 - min_x1,
                "height": max_y2 - min_y1,
                "area": (max_x2 - min_x1) * (max_y2 - min_y1),
                "is_merged": True,
                "merged_cells": [cell["row"] * 1000 + cell["col"] for cell in all_cells],
                "text": "",
                "confidence": 0.0
            }
            
            return merged_cell
            
        except Exception as e:
            logger.error(f"创建合并单元格失败: {e}")
            return main_cell
    
    def _infer_grid_positions(self, cells: List[Dict]) -> List[Dict]:
        """推断单元格的网格位置"""
        try:
            if not cells:
                return cells
            
            # 按Y坐标排序确定行
            cells_by_y = sorted(cells, key=lambda x: x["bbox"]["y1"])
            
            # 分组为行
            rows = []
            current_row = [cells_by_y[0]]
            current_y = cells_by_y[0]["bbox"]["y1"]
            
            for cell in cells_by_y[1:]:
                if abs(cell["bbox"]["y1"] - current_y) <= 10:  # 同一行
                    current_row.append(cell)
                else:
                    rows.append(current_row)
                    current_row = [cell]
                    current_y = cell["bbox"]["y1"]
            
            if current_row:
                rows.append(current_row)
            
            # 为每行的单元格分配列位置
            for row_idx, row_cells in enumerate(rows):
                # 按X坐标排序
                row_cells.sort(key=lambda x: x["bbox"]["x1"])
                
                for col_idx, cell in enumerate(row_cells):
                    cell["row"] = row_idx
                    cell["col"] = col_idx
            
            return cells
            
        except Exception as e:
            logger.error(f"推断网格位置失败: {e}")
            return cells
    
    def _validate_cells(self, cells: List[Dict]) -> List[Dict]:
        """验证和过滤单元格"""
        valid_cells = []
        
        for cell in cells:
            # 检查尺寸
            if (cell["width"] >= 10 and cell["height"] >= 10 and 
                cell["area"] >= self.min_cell_area):
                
                # 检查宽高比
                aspect_ratio = cell["width"] / cell["height"]
                if 0.1 <= aspect_ratio <= 10:  # 合理的宽高比
                    valid_cells.append(cell)
        
        return valid_cells
