"""
健康检查路由
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import time
import psutil
from datetime import datetime
from loguru import logger

from src.offerDiff.api.dependencies import get_database, get_redis, get_task_queue
from src.offerDiff.api.models.health import HealthResponse, SystemStatus, ServiceStatus
from src.offerDiff.api.models.common import APIResponse


router = APIRouter(prefix="/health")


@router.get("/", response_model=HealthResponse)
async def health_check():
    """
    基础健康检查
    """
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow(),
        version="1.0.0",
        message="服务运行正常"
    )


@router.get("/detailed", response_model=Dict[str, Any])
async def detailed_health_check(
    db=Depends(get_database),
    redis=Depends(get_redis),
    task_queue=Depends(get_task_queue)
):
    """
    详细健康检查
    """
    try:
        start_time = time.time()
        
        # 检查各个服务状态
        services = {}
        
        # 数据库检查
        try:
            db_start = time.time()
            await db.execute("SELECT 1")
            db_time = time.time() - db_start
            services["database"] = ServiceStatus(
                status="healthy",
                response_time=db_time,
                message="数据库连接正常"
            )
        except Exception as e:
            services["database"] = ServiceStatus(
                status="unhealthy",
                response_time=0,
                message=f"数据库连接失败: {str(e)}"
            )
        
        # Redis检查
        if redis:
            try:
                redis_start = time.time()
                await redis.ping()
                redis_time = time.time() - redis_start
                services["redis"] = ServiceStatus(
                    status="healthy",
                    response_time=redis_time,
                    message="Redis连接正常"
                )
            except Exception as e:
                services["redis"] = ServiceStatus(
                    status="unhealthy",
                    response_time=0,
                    message=f"Redis连接失败: {str(e)}"
                )
        else:
            services["redis"] = ServiceStatus(
                status="disabled",
                response_time=0,
                message="Redis未配置"
            )
        
        # 任务队列检查
        if task_queue:
            try:
                queue_start = time.time()
                queue_info = await task_queue.get_queue_info()
                queue_time = time.time() - queue_start
                services["task_queue"] = ServiceStatus(
                    status="healthy",
                    response_time=queue_time,
                    message=f"任务队列正常，待处理任务: {queue_info.get('pending', 0)}"
                )
            except Exception as e:
                services["task_queue"] = ServiceStatus(
                    status="unhealthy",
                    response_time=0,
                    message=f"任务队列连接失败: {str(e)}"
                )
        else:
            services["task_queue"] = ServiceStatus(
                status="disabled",
                response_time=0,
                message="任务队列未配置"
            )
        
        # 系统资源检查
        system_info = await _get_system_info()
        
        # 确定整体状态
        overall_status = "healthy"
        unhealthy_services = [name for name, service in services.items() if service.status == "unhealthy"]
        
        if unhealthy_services:
            overall_status = "degraded" if len(unhealthy_services) < len(services) else "unhealthy"
        
        total_time = time.time() - start_time
        
        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "response_time": total_time,
            "services": {name: service.dict() for name, service in services.items()},
            "system": system_info,
            "unhealthy_services": unhealthy_services
        }
        
    except Exception as e:
        logger.error(f"详细健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "error": str(e)
        }


@router.get("/system")
async def system_status():
    """
    系统状态检查
    """
    try:
        system_info = await _get_system_info()
        
        return APIResponse(
            success=True,
            data=system_info,
            message="系统状态获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取系统状态失败: {str(e)}"
        )


@router.get("/services")
async def services_status(
    db=Depends(get_database),
    redis=Depends(get_redis),
    task_queue=Depends(get_task_queue)
):
    """
    服务状态检查
    """
    try:
        services = {}
        
        # 检查数据库
        try:
            await db.execute("SELECT 1")
            services["database"] = {"status": "healthy", "message": "连接正常"}
        except Exception as e:
            services["database"] = {"status": "unhealthy", "message": str(e)}
        
        # 检查Redis
        if redis:
            try:
                await redis.ping()
                services["redis"] = {"status": "healthy", "message": "连接正常"}
            except Exception as e:
                services["redis"] = {"status": "unhealthy", "message": str(e)}
        else:
            services["redis"] = {"status": "disabled", "message": "未配置"}
        
        # 检查任务队列
        if task_queue:
            try:
                queue_info = await task_queue.get_queue_info()
                services["task_queue"] = {
                    "status": "healthy",
                    "message": "连接正常",
                    "queue_info": queue_info
                }
            except Exception as e:
                services["task_queue"] = {"status": "unhealthy", "message": str(e)}
        else:
            services["task_queue"] = {"status": "disabled", "message": "未配置"}
        
        return APIResponse(
            success=True,
            data=services,
            message="服务状态获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取服务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取服务状态失败: {str(e)}"
        )


@router.get("/metrics")
async def get_metrics(
    db=Depends(get_database)
):
    """
    获取系统指标
    """
    try:
        # 获取系统指标
        system_metrics = await _get_system_metrics()
        
        # 获取应用指标
        app_metrics = await _get_app_metrics(db)
        
        metrics = {
            "system": system_metrics,
            "application": app_metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return APIResponse(
            success=True,
            data=metrics,
            message="系统指标获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取系统指标失败: {str(e)}"
        )


async def _get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    try:
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存信息
        memory = psutil.virtual_memory()
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        
        # 网络信息
        network = psutil.net_io_counters()
        
        return {
            "cpu": {
                "usage_percent": cpu_percent,
                "count": cpu_count
            },
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "usage_percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "usage_percent": (disk.used / disk.total) * 100
            },
            "network": {
                "bytes_sent": network.bytes_sent,
                "bytes_recv": network.bytes_recv,
                "packets_sent": network.packets_sent,
                "packets_recv": network.packets_recv
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        return {"error": str(e)}


async def _get_system_metrics() -> Dict[str, Any]:
    """获取系统指标"""
    try:
        # 进程信息
        process = psutil.Process()
        
        return {
            "process": {
                "pid": process.pid,
                "cpu_percent": process.cpu_percent(),
                "memory_info": process.memory_info()._asdict(),
                "create_time": process.create_time(),
                "num_threads": process.num_threads()
            },
            "system": await _get_system_info()
        }
        
    except Exception as e:
        logger.error(f"获取系统指标失败: {e}")
        return {"error": str(e)}


async def _get_app_metrics(db) -> Dict[str, Any]:
    """获取应用指标"""
    try:
        # 查询数据库统计
        total_comparisons = await db.fetch_val("SELECT COUNT(*) FROM comparison_tasks")
        completed_comparisons = await db.fetch_val(
            "SELECT COUNT(*) FROM comparison_tasks WHERE status = 'completed'"
        )
        failed_comparisons = await db.fetch_val(
            "SELECT COUNT(*) FROM comparison_tasks WHERE status = 'failed'"
        )
        
        # 查询最近24小时的统计
        recent_comparisons = await db.fetch_val(
            "SELECT COUNT(*) FROM comparison_tasks WHERE created_at > NOW() - INTERVAL '24 hours'"
        )
        
        return {
            "comparisons": {
                "total": total_comparisons or 0,
                "completed": completed_comparisons or 0,
                "failed": failed_comparisons or 0,
                "recent_24h": recent_comparisons or 0,
                "success_rate": (completed_comparisons / total_comparisons * 100) if total_comparisons > 0 else 0
            }
        }
        
    except Exception as e:
        logger.error(f"获取应用指标失败: {e}")
        return {"error": str(e)}
