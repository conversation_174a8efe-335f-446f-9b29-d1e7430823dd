"""
文档比对路由
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from typing import Optional, List
from datetime import datetime
from loguru import logger

from src.offerDiff.api.dependencies import get_database, get_task_queue
from src.offerDiff.api.models.comparison import (
    ComparisonRequest, ComparisonResponse, ComparisonStatus,
    ComparisonListResponse, ComparisonDetailResponse
)
from src.offerDiff.api.models.common import APIResponse
from src.offerDiff.services.comparison_service import ComparisonService
from src.offerDiff.database.models import ComparisonTask


router = APIRouter(prefix="/comparison")


@router.post("/submit", response_model=ComparisonResponse)
async def submit_comparison(
    request: ComparisonRequest,
    background_tasks: BackgroundTasks,
    db=Depends(get_database),
    task_queue=Depends(get_task_queue)
):
    """
    提交文档比对任务
    
    - **document1_id**: 第一个文档的ID
    - **document2_id**: 第二个文档的ID
    - **comparison_options**: 比对选项配置
    - **priority**: 任务优先级 (1-10, 默认5)
    """
    try:
        comparison_service = ComparisonService(db, task_queue)
        
        # 验证文档存在性
        if not await comparison_service.validate_documents(request.document1_id, request.document2_id):
            raise HTTPException(
                status_code=400,
                detail="指定的文档不存在或不可访问"
            )
        
        # 创建比对任务
        task = await comparison_service.create_comparison_task(
            document1_id=request.document1_id,
            document2_id=request.document2_id,
            options=request.comparison_options,
            priority=request.priority
        )
        
        # 提交到任务队列
        if request.async_processing:
            # 异步处理
            await comparison_service.submit_async_task(task.id)
            status = ComparisonStatus.QUEUED
        else:
            # 同步处理
            background_tasks.add_task(comparison_service.process_comparison_sync, task.id)
            status = ComparisonStatus.PROCESSING
        
        logger.info(f"比对任务已提交: {task.id}")
        
        return ComparisonResponse(
            task_id=task.id,
            status=status,
            message="比对任务已成功提交",
            estimated_time=await comparison_service.estimate_processing_time(task.id),
            created_at=task.created_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交比对任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"提交比对任务失败: {str(e)}"
        )


@router.get("/status/{task_id}", response_model=ComparisonDetailResponse)
async def get_comparison_status(
    task_id: str,
    db=Depends(get_database)
):
    """
    查询比对任务状态
    
    - **task_id**: 比对任务ID
    """
    try:
        comparison_service = ComparisonService(db)
        
        # 获取任务详情
        task = await comparison_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(
                status_code=404,
                detail="比对任务不存在"
            )
        
        # 获取处理进度
        progress = await comparison_service.get_task_progress(task_id)
        
        # 获取结果（如果已完成）
        result = None
        if task.status == ComparisonStatus.COMPLETED:
            result = await comparison_service.get_comparison_result(task_id)
        
        return ComparisonDetailResponse(
            task_id=task.id,
            status=task.status,
            progress=progress,
            result=result,
            error_message=task.error_message,
            created_at=task.created_at,
            updated_at=task.updated_at,
            completed_at=task.completed_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询比对任务状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"查询任务状态失败: {str(e)}"
        )


@router.get("/list", response_model=ComparisonListResponse)
async def list_comparisons(
    status: Optional[ComparisonStatus] = Query(None, description="按状态筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db=Depends(get_database)
):
    """
    获取比对任务列表
    
    - **status**: 可选的状态筛选
    - **limit**: 返回数量限制 (1-100)
    - **offset**: 偏移量
    """
    try:
        comparison_service = ComparisonService(db)
        
        # 获取任务列表
        tasks, total = await comparison_service.list_tasks(
            status=status,
            limit=limit,
            offset=offset
        )
        
        return ComparisonListResponse(
            tasks=tasks,
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"获取比对任务列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取任务列表失败: {str(e)}"
        )


@router.get("/result/{task_id}")
async def get_comparison_result(
    task_id: str,
    format: str = Query("json", regex="^(json|summary)$", description="结果格式"),
    db=Depends(get_database)
):
    """
    获取比对结果
    
    - **task_id**: 比对任务ID
    - **format**: 结果格式 (json/summary)
    """
    try:
        comparison_service = ComparisonService(db)
        
        # 检查任务状态
        task = await comparison_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(
                status_code=404,
                detail="比对任务不存在"
            )
        
        if task.status != ComparisonStatus.COMPLETED:
            raise HTTPException(
                status_code=400,
                detail=f"任务尚未完成，当前状态: {task.status}"
            )
        
        # 获取比对结果
        result = await comparison_service.get_comparison_result(task_id)
        if not result:
            raise HTTPException(
                status_code=404,
                detail="比对结果不存在"
            )
        
        if format == "summary":
            # 返回摘要格式
            summary = await comparison_service.get_result_summary(result)
            return APIResponse(
                success=True,
                data=summary,
                message="比对结果摘要获取成功"
            )
        else:
            # 返回完整JSON格式
            return APIResponse(
                success=True,
                data=result,
                message="比对结果获取成功"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取比对结果失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取比对结果失败: {str(e)}"
        )


@router.delete("/cancel/{task_id}")
async def cancel_comparison(
    task_id: str,
    db=Depends(get_database),
    task_queue=Depends(get_task_queue)
):
    """
    取消比对任务
    
    - **task_id**: 比对任务ID
    """
    try:
        comparison_service = ComparisonService(db, task_queue)
        
        # 检查任务状态
        task = await comparison_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(
                status_code=404,
                detail="比对任务不存在"
            )
        
        if task.status in [ComparisonStatus.COMPLETED, ComparisonStatus.FAILED, ComparisonStatus.CANCELLED]:
            raise HTTPException(
                status_code=400,
                detail=f"任务已结束，无法取消，当前状态: {task.status}"
            )
        
        # 取消任务
        success = await comparison_service.cancel_task(task_id)
        
        if success:
            return APIResponse(
                success=True,
                message="比对任务已成功取消"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="任务取消失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消比对任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"取消任务失败: {str(e)}"
        )


@router.post("/retry/{task_id}")
async def retry_comparison(
    task_id: str,
    background_tasks: BackgroundTasks,
    db=Depends(get_database),
    task_queue=Depends(get_task_queue)
):
    """
    重试失败的比对任务
    
    - **task_id**: 比对任务ID
    """
    try:
        comparison_service = ComparisonService(db, task_queue)
        
        # 检查任务状态
        task = await comparison_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(
                status_code=404,
                detail="比对任务不存在"
            )
        
        if task.status != ComparisonStatus.FAILED:
            raise HTTPException(
                status_code=400,
                detail=f"只能重试失败的任务，当前状态: {task.status}"
            )
        
        # 重置任务状态
        await comparison_service.reset_task_for_retry(task_id)
        
        # 重新提交任务
        await comparison_service.submit_async_task(task_id)
        
        logger.info(f"比对任务重试已提交: {task_id}")
        
        return APIResponse(
            success=True,
            message="比对任务重试已提交"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试比对任务失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"重试任务失败: {str(e)}"
        )


@router.get("/statistics")
async def get_comparison_statistics(
    days: int = Query(7, ge=1, le=365, description="统计天数"),
    db=Depends(get_database)
):
    """
    获取比对统计信息
    
    - **days**: 统计天数 (1-365)
    """
    try:
        comparison_service = ComparisonService(db)
        
        # 获取统计信息
        statistics = await comparison_service.get_statistics(days)
        
        return APIResponse(
            success=True,
            data=statistics,
            message="统计信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取比对统计信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计信息失败: {str(e)}"
        )
