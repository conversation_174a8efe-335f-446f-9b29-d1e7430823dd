"""
文件上传路由
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse
from typing import Optional, List
import os
from pathlib import Path
from loguru import logger

from src.offerDiff.api.dependencies import get_database, get_file_manager
from src.offerDiff.api.models.upload import UploadResponse, FileInfo, FileListResponse
from src.offerDiff.api.models.common import APIResponse
from src.offerDiff.services.upload_service import UploadService
from config.settings import settings


router = APIRouter(prefix="/upload")


@router.post("/document", response_model=UploadResponse)
async def upload_document(
    file: UploadFile = File(..., description="上传的文档文件"),
    document_type: Optional[str] = Form(None, description="文档类型"),
    description: Optional[str] = Form(None, description="文档描述"),
    db=Depends(get_database),
    file_manager=Depends(get_file_manager)
):
    """
    上传文档文件
    
    - **file**: 文档文件 (支持PDF、JPG、PNG等格式)
    - **document_type**: 文档类型 (可选)
    - **description**: 文档描述 (可选)
    """
    try:
        upload_service = UploadService(db, file_manager)
        
        # 验证文件
        validation_result = await upload_service.validate_file(file)
        if not validation_result["valid"]:
            raise HTTPException(
                status_code=400,
                detail=validation_result["error"]
            )
        
        # 上传文件
        upload_result = await upload_service.upload_document(
            file=file,
            document_type=document_type,
            description=description
        )
        
        logger.info(f"文档上传成功: {upload_result['file_id']}")
        
        return UploadResponse(
            file_id=upload_result["file_id"],
            filename=upload_result["filename"],
            file_size=upload_result["file_size"],
            file_type=upload_result["file_type"],
            upload_url=upload_result["upload_url"],
            message="文件上传成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文档上传失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文件上传失败: {str(e)}"
        )


@router.post("/batch", response_model=List[UploadResponse])
async def upload_batch_documents(
    files: List[UploadFile] = File(..., description="批量上传的文档文件"),
    document_type: Optional[str] = Form(None, description="文档类型"),
    db=Depends(get_database),
    file_manager=Depends(get_file_manager)
):
    """
    批量上传文档文件
    
    - **files**: 文档文件列表 (最多10个文件)
    - **document_type**: 文档类型 (可选)
    """
    try:
        if len(files) > settings.max_batch_upload_files:
            raise HTTPException(
                status_code=400,
                detail=f"批量上传文件数量不能超过{settings.max_batch_upload_files}个"
            )
        
        upload_service = UploadService(db, file_manager)
        
        results = []
        failed_files = []
        
        for file in files:
            try:
                # 验证文件
                validation_result = await upload_service.validate_file(file)
                if not validation_result["valid"]:
                    failed_files.append({
                        "filename": file.filename,
                        "error": validation_result["error"]
                    })
                    continue
                
                # 上传文件
                upload_result = await upload_service.upload_document(
                    file=file,
                    document_type=document_type
                )
                
                results.append(UploadResponse(
                    file_id=upload_result["file_id"],
                    filename=upload_result["filename"],
                    file_size=upload_result["file_size"],
                    file_type=upload_result["file_type"],
                    upload_url=upload_result["upload_url"],
                    message="文件上传成功"
                ))
                
            except Exception as e:
                failed_files.append({
                    "filename": file.filename,
                    "error": str(e)
                })
        
        if failed_files:
            logger.warning(f"批量上传中有{len(failed_files)}个文件失败")
        
        logger.info(f"批量上传完成: 成功{len(results)}个，失败{len(failed_files)}个")
        
        # 如果有失败的文件，在响应中包含错误信息
        if failed_files and not results:
            raise HTTPException(
                status_code=400,
                detail={"message": "所有文件上传失败", "failed_files": failed_files}
            )
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量上传失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量上传失败: {str(e)}"
        )


@router.get("/info/{file_id}", response_model=FileInfo)
async def get_file_info(
    file_id: str,
    db=Depends(get_database)
):
    """
    获取文件信息
    
    - **file_id**: 文件ID
    """
    try:
        upload_service = UploadService(db)
        
        file_info = await upload_service.get_file_info(file_id)
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        return FileInfo(**file_info)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文件信息失败: {str(e)}"
        )


@router.get("/list", response_model=FileListResponse)
async def list_files(
    file_type: Optional[str] = None,
    document_type: Optional[str] = None,
    limit: int = 20,
    offset: int = 0,
    db=Depends(get_database)
):
    """
    获取文件列表
    
    - **file_type**: 文件类型筛选 (可选)
    - **document_type**: 文档类型筛选 (可选)
    - **limit**: 返回数量限制
    - **offset**: 偏移量
    """
    try:
        upload_service = UploadService(db)
        
        files, total = await upload_service.list_files(
            file_type=file_type,
            document_type=document_type,
            limit=limit,
            offset=offset
        )
        
        return FileListResponse(
            files=[FileInfo(**file) for file in files],
            total=total,
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.error(f"获取文件列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取文件列表失败: {str(e)}"
        )


@router.delete("/delete/{file_id}")
async def delete_file(
    file_id: str,
    db=Depends(get_database),
    file_manager=Depends(get_file_manager)
):
    """
    删除文件
    
    - **file_id**: 文件ID
    """
    try:
        upload_service = UploadService(db, file_manager)
        
        # 检查文件是否存在
        file_info = await upload_service.get_file_info(file_id)
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 检查文件是否正在使用
        is_in_use = await upload_service.check_file_in_use(file_id)
        if is_in_use:
            raise HTTPException(
                status_code=400,
                detail="文件正在被比对任务使用，无法删除"
            )
        
        # 删除文件
        success = await upload_service.delete_file(file_id)
        
        if success:
            logger.info(f"文件删除成功: {file_id}")
            return APIResponse(
                success=True,
                message="文件删除成功"
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="文件删除失败"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除文件失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"删除文件失败: {str(e)}"
        )


@router.get("/download/{file_id}")
async def download_file(
    file_id: str,
    db=Depends(get_database),
    file_manager=Depends(get_file_manager)
):
    """
    下载文件
    
    - **file_id**: 文件ID
    """
    try:
        upload_service = UploadService(db, file_manager)
        
        # 获取文件信息
        file_info = await upload_service.get_file_info(file_id)
        if not file_info:
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        # 生成下载URL
        download_url = await upload_service.generate_download_url(file_id)
        
        return APIResponse(
            success=True,
            data={
                "download_url": download_url,
                "filename": file_info["filename"],
                "file_size": file_info["file_size"],
                "expires_in": 3600  # 1小时过期
            },
            message="下载链接生成成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成下载链接失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成下载链接失败: {str(e)}"
        )


@router.post("/validate")
async def validate_file_format(
    file: UploadFile = File(..., description="待验证的文件"),
    db=Depends(get_database)
):
    """
    验证文件格式
    
    - **file**: 待验证的文件
    """
    try:
        upload_service = UploadService(db)
        
        # 验证文件
        validation_result = await upload_service.validate_file(file)
        
        return APIResponse(
            success=validation_result["valid"],
            data={
                "valid": validation_result["valid"],
                "file_type": validation_result.get("file_type"),
                "file_size": validation_result.get("file_size"),
                "supported_formats": settings.allowed_file_types
            },
            message=validation_result.get("error", "文件格式验证通过")
        )
        
    except Exception as e:
        logger.error(f"文件格式验证失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"文件格式验证失败: {str(e)}"
        )


@router.get("/supported-formats")
async def get_supported_formats():
    """
    获取支持的文件格式
    """
    return APIResponse(
        success=True,
        data={
            "supported_formats": settings.allowed_file_types,
            "max_file_size": settings.max_file_size,
            "max_batch_files": settings.max_batch_upload_files
        },
        message="支持的文件格式信息"
    )
