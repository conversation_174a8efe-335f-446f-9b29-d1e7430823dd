"""
结果分析路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import FileResponse, JSONResponse
from typing import Optional, List
from datetime import datetime, timedelta
from loguru import logger

from src.offerDiff.api.dependencies import get_database
from src.offerDiff.api.models.analysis import (
    AnalysisRequest, AnalysisResponse, ReportRequest, ReportResponse,
    StatisticsResponse, TrendAnalysisResponse
)
from src.offerDiff.api.models.common import APIResponse
from src.offerDiff.services.analysis_service import AnalysisService


router = APIRouter(prefix="/analysis")


@router.post("/analyze/{task_id}", response_model=AnalysisResponse)
async def analyze_comparison_result(
    task_id: str,
    request: AnalysisRequest,
    db=Depends(get_database)
):
    """
    分析比对结果
    
    - **task_id**: 比对任务ID
    - **analysis_options**: 分析选项配置
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 检查比对任务是否存在且已完成
        task = await analysis_service.get_comparison_task(task_id)
        if not task:
            raise HTTPException(
                status_code=404,
                detail="比对任务不存在"
            )
        
        if task.status != "completed":
            raise HTTPException(
                status_code=400,
                detail=f"比对任务尚未完成，当前状态: {task.status}"
            )
        
        # 执行分析
        analysis_result = await analysis_service.analyze_comparison_result(
            task_id=task_id,
            options=request.analysis_options
        )
        
        logger.info(f"比对结果分析完成: {task_id}")
        
        return AnalysisResponse(
            analysis_id=analysis_result["analysis_id"],
            task_id=task_id,
            risk_level=analysis_result["risk_assessment"]["risk_level"],
            quality_score=analysis_result["quality_assessment"]["overall_quality"],
            summary=analysis_result["summary"],
            created_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析比对结果失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"分析失败: {str(e)}"
        )


@router.get("/result/{analysis_id}")
async def get_analysis_result(
    analysis_id: str,
    include_details: bool = Query(True, description="是否包含详细信息"),
    db=Depends(get_database)
):
    """
    获取分析结果
    
    - **analysis_id**: 分析ID
    - **include_details**: 是否包含详细信息
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 获取分析结果
        result = await analysis_service.get_analysis_result(
            analysis_id=analysis_id,
            include_details=include_details
        )
        
        if not result:
            raise HTTPException(
                status_code=404,
                detail="分析结果不存在"
            )
        
        return APIResponse(
            success=True,
            data=result,
            message="分析结果获取成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分析结果失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取分析结果失败: {str(e)}"
        )


@router.post("/report/generate", response_model=ReportResponse)
async def generate_report(
    request: ReportRequest,
    db=Depends(get_database)
):
    """
    生成分析报告
    
    - **analysis_id**: 分析ID
    - **report_format**: 报告格式 (html/pdf/json)
    - **template**: 报告模板 (可选)
    - **include_charts**: 是否包含图表
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 检查分析结果是否存在
        analysis_result = await analysis_service.get_analysis_result(request.analysis_id)
        if not analysis_result:
            raise HTTPException(
                status_code=404,
                detail="分析结果不存在"
            )
        
        # 生成报告
        report_result = await analysis_service.generate_report(
            analysis_id=request.analysis_id,
            format=request.report_format,
            template=request.template,
            include_charts=request.include_charts
        )
        
        logger.info(f"分析报告生成完成: {report_result['report_id']}")
        
        return ReportResponse(
            report_id=report_result["report_id"],
            report_url=report_result["report_url"],
            format=request.report_format,
            file_size=report_result["file_size"],
            generated_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成分析报告失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"生成报告失败: {str(e)}"
        )


@router.get("/report/download/{report_id}")
async def download_report(
    report_id: str,
    db=Depends(get_database)
):
    """
    下载分析报告
    
    - **report_id**: 报告ID
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 获取报告信息
        report_info = await analysis_service.get_report_info(report_id)
        if not report_info:
            raise HTTPException(
                status_code=404,
                detail="报告不存在"
            )
        
        # 检查文件是否存在
        file_path = report_info["file_path"]
        if not file_path or not Path(file_path).exists():
            raise HTTPException(
                status_code=404,
                detail="报告文件不存在"
            )
        
        # 返回文件
        return FileResponse(
            path=file_path,
            filename=report_info["filename"],
            media_type=report_info["media_type"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下载分析报告失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"下载报告失败: {str(e)}"
        )


@router.get("/statistics", response_model=StatisticsResponse)
async def get_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    group_by: str = Query("day", regex="^(day|week|month)$", description="分组方式"),
    db=Depends(get_database)
):
    """
    获取统计信息
    
    - **start_date**: 开始日期 (可选，默认30天前)
    - **end_date**: 结束日期 (可选，默认今天)
    - **group_by**: 分组方式 (day/week/month)
    """
    try:
        # 设置默认日期范围
        if not end_date:
            end_date = datetime.utcnow()
        if not start_date:
            start_date = end_date - timedelta(days=30)
        
        analysis_service = AnalysisService(db)
        
        # 获取统计信息
        statistics = await analysis_service.get_statistics(
            start_date=start_date,
            end_date=end_date,
            group_by=group_by
        )
        
        return StatisticsResponse(
            period={
                "start_date": start_date,
                "end_date": end_date,
                "group_by": group_by
            },
            summary=statistics["summary"],
            trends=statistics["trends"],
            distributions=statistics["distributions"]
        )
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取统计信息失败: {str(e)}"
        )


@router.get("/trends", response_model=TrendAnalysisResponse)
async def get_trend_analysis(
    metric: str = Query("similarity", regex="^(similarity|processing_time|difference_count)$", description="分析指标"),
    period: int = Query(30, ge=7, le=365, description="分析周期（天）"),
    db=Depends(get_database)
):
    """
    获取趋势分析
    
    - **metric**: 分析指标 (similarity/processing_time/difference_count)
    - **period**: 分析周期，天数 (7-365)
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 获取趋势分析
        trend_analysis = await analysis_service.get_trend_analysis(
            metric=metric,
            period=period
        )
        
        return TrendAnalysisResponse(
            metric=metric,
            period=period,
            trend_direction=trend_analysis["trend_direction"],
            trend_strength=trend_analysis["trend_strength"],
            data_points=trend_analysis["data_points"],
            predictions=trend_analysis.get("predictions", [])
        )
        
    except Exception as e:
        logger.error(f"获取趋势分析失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取趋势分析失败: {str(e)}"
        )


@router.get("/insights")
async def get_insights(
    limit: int = Query(10, ge=1, le=50, description="返回数量限制"),
    db=Depends(get_database)
):
    """
    获取分析洞察
    
    - **limit**: 返回数量限制 (1-50)
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 获取分析洞察
        insights = await analysis_service.get_insights(limit=limit)
        
        return APIResponse(
            success=True,
            data={
                "insights": insights,
                "generated_at": datetime.utcnow().isoformat()
            },
            message="分析洞察获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取分析洞察失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取分析洞察失败: {str(e)}"
        )


@router.delete("/cleanup")
async def cleanup_old_data(
    days: int = Query(90, ge=30, le=365, description="保留天数"),
    dry_run: bool = Query(True, description="是否为试运行"),
    db=Depends(get_database)
):
    """
    清理旧数据
    
    - **days**: 保留天数 (30-365)
    - **dry_run**: 是否为试运行（不实际删除）
    """
    try:
        analysis_service = AnalysisService(db)
        
        # 执行清理
        cleanup_result = await analysis_service.cleanup_old_data(
            days=days,
            dry_run=dry_run
        )
        
        return APIResponse(
            success=True,
            data=cleanup_result,
            message="数据清理完成" if not dry_run else "数据清理预览完成"
        )
        
    except Exception as e:
        logger.error(f"清理旧数据失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"清理数据失败: {str(e)}"
        )
