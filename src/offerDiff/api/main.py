"""
FastAPI主应用
"""
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import time
import uuid
from loguru import logger

from config.settings import settings
from src.offerDiff.offerDiff.api.routes import comparison, upload, analysis, health
from src.offerDiff.offerDiff.api.middleware import setup_middleware
from src.offerDiff.offerDiff.api.exceptions import setup_exception_handlers


# 创建FastAPI应用
app = FastAPI(
    title="合同比对系统API",
    description="基于OCR和大模型的智能合同比对系统",
    version="1.0.0",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None
)

# 设置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加Gzip压缩
app.add_middleware(GZipMiddleware, minimum_size=1000)

# 设置中间件
setup_middleware(app)

# 设置异常处理器
setup_exception_handlers(app)

# 注册路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(upload.router, prefix="/api/v1", tags=["文件上传"])
app.include_router(comparison.router, prefix="/api/v1", tags=["文档比对"])
app.include_router(analysis.router, prefix="/api/v1", tags=["结果分析"])


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加处理时间头"""
    start_time = time.time()
    
    # 生成请求ID
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id
    
    # 记录请求开始
    logger.info(f"Request started: {request.method} {request.url} [ID: {request_id}]")
    
    try:
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Request-ID"] = request_id
        
        # 记录请求完成
        logger.info(f"Request completed: {request.method} {request.url} "
                   f"[ID: {request_id}] [Time: {process_time:.3f}s] [Status: {response.status_code}]")
        
        return response
        
    except Exception as e:
        # 记录请求异常
        process_time = time.time() - start_time
        logger.error(f"Request failed: {request.method} {request.url} "
                    f"[ID: {request_id}] [Time: {process_time:.3f}s] [Error: {str(e)}]")
        raise


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("合同比对系统API启动中...")
    
    try:
        # 初始化数据库连接
        from src.offerDiff.database.connection import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        logger.info("数据库连接初始化完成")
        
        # 初始化Redis连接
        if settings.redis_url:
            from src.offerDiff.cache.redis_client import RedisClient
            redis_client = RedisClient()
            await redis_client.initialize()
            logger.info("Redis连接初始化完成")
        
        # 初始化任务队列
        if settings.enable_task_queue:
            from src.offerDiff.tasks.task_manager import TaskManager
            task_manager = TaskManager()
            await task_manager.initialize()
            logger.info("任务队列初始化完成")
        
        # 初始化文件存储
        from src.offerDiff.storage.file_manager import FileManager
        file_manager = FileManager()
        await file_manager.initialize()
        logger.info("文件存储初始化完成")
        
        logger.info("合同比对系统API启动完成")
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("合同比对系统API关闭中...")
    
    try:
        # 关闭数据库连接
        from src.offerDiff.database.connection import DatabaseManager
        db_manager = DatabaseManager()
        await db_manager.close()
        logger.info("数据库连接已关闭")
        
        # 关闭Redis连接
        if settings.redis_url:
            from src.offerDiff.cache.redis_client import RedisClient
            redis_client = RedisClient()
            await redis_client.close()
            logger.info("Redis连接已关闭")
        
        # 关闭任务队列
        if settings.enable_task_queue:
            from src.offerDiff.tasks.task_manager import TaskManager
            task_manager = TaskManager()
            await task_manager.close()
            logger.info("任务队列已关闭")
        
        logger.info("合同比对系统API关闭完成")
        
    except Exception as e:
        logger.error(f"应用关闭异常: {e}")


@app.get("/", include_in_schema=False)
async def root():
    """根路径"""
    return {
        "message": "合同比对系统API",
        "version": "1.0.0",
        "status": "running",
        "docs_url": "/docs" if settings.debug else None
    }


@app.get("/api", include_in_schema=False)
async def api_info():
    """API信息"""
    return {
        "name": "合同比对系统API",
        "version": "1.0.0",
        "description": "基于OCR和大模型的智能合同比对系统",
        "endpoints": {
            "health": "/api/v1/health",
            "upload": "/api/v1/upload",
            "comparison": "/api/v1/comparison",
            "analysis": "/api/v1/analysis"
        },
        "documentation": {
            "swagger": "/docs" if settings.debug else None,
            "redoc": "/redoc" if settings.debug else None,
            "openapi": "/openapi.json" if settings.debug else None
        }
    }


def custom_openapi():
    """自定义OpenAPI文档"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="合同比对系统API",
        version="1.0.0",
        description="""
        ## 合同比对系统API文档
        
        基于OCR和大模型的智能合同比对系统，提供以下主要功能：
        
        ### 核心功能
        - **文档上传**: 支持PDF、图片等格式的合同文档上传
        - **OCR识别**: 高精度的文本和表格识别
        - **智能比对**: 多维度的文档内容比对分析
        - **结果分析**: 详细的差异分析和风险评估
        - **报告生成**: 多格式的比对报告输出
        
        ### 技术特性
        - 异步处理支持
        - 实时状态查询
        - 缓存优化
        - 错误重试机制
        - 详细的日志记录
        
        ### 使用流程
        1. 上传待比对的文档文件
        2. 提交比对任务
        3. 查询处理状态
        4. 获取比对结果
        5. 生成分析报告
        """,
        routes=app.routes,
    )
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key"
        }
    }
    
    # 添加标签描述
    openapi_schema["tags"] = [
        {
            "name": "健康检查",
            "description": "系统健康状态检查接口"
        },
        {
            "name": "文件上传",
            "description": "文档文件上传和管理接口"
        },
        {
            "name": "文档比对",
            "description": "文档比对任务管理和结果查询接口"
        },
        {
            "name": "结果分析",
            "description": "比对结果分析和报告生成接口"
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# 错误处理
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """404错误处理"""
    return JSONResponse(
        status_code=404,
        content={
            "error": "Not Found",
            "message": "请求的资源不存在",
            "path": str(request.url.path),
            "request_id": getattr(request.state, "request_id", None)
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    """500错误处理"""
    logger.error(f"Internal server error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal Server Error",
            "message": "服务器内部错误",
            "request_id": getattr(request.state, "request_id", None)
        }
    )


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level="info" if settings.debug else "warning"
    )
