"""
比对相关数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, Dict, List, Any
from datetime import datetime
from enum import Enum

from .common import PaginationResponse, TaskProgress


class ComparisonStatus(str, Enum):
    """比对任务状态"""
    PENDING = "pending"
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ComparisonOptions(BaseModel):
    """比对选项配置"""
    # 权重配置
    weights: Optional[Dict[str, float]] = Field(
        default={
            "text_similarity": 0.25,
            "layout_similarity": 0.20,
            "financial_consistency": 0.30,
            "semantic_similarity": 0.25
        },
        description="各项比对权重"
    )
    
    # 阈值配置
    thresholds: Optional[Dict[str, float]] = Field(
        default={
            "high_similarity": 0.9,
            "medium_similarity": 0.7,
            "low_similarity": 0.5
        },
        description="相似度阈值"
    )
    
    # 文本比对选项
    text_options: Optional[Dict[str, Any]] = Field(
        default={
            "method": "comprehensive",
            "ignore_case": True,
            "ignore_whitespace": True
        },
        description="文本比对选项"
    )
    
    # 布局比对选项
    layout_options: Optional[Dict[str, Any]] = Field(
        default={
            "position_tolerance": 10,
            "size_tolerance": 0.1,
            "alignment_tolerance": 5
        },
        description="布局比对选项"
    )
    
    # 财务比对选项
    financial_options: Optional[Dict[str, Any]] = Field(
        default={
            "amount_tolerance": 0.01,
            "percentage_tolerance": 0.001
        },
        description="财务比对选项"
    )
    
    # 语义比对选项
    semantic_options: Optional[Dict[str, Any]] = Field(
        default={
            "use_llm": True,
            "similarity_threshold": 0.8,
            "confidence_threshold": 0.7
        },
        description="语义比对选项"
    )
    
    # 其他选项
    enable_caching: bool = Field(True, description="是否启用缓存")
    max_processing_time: int = Field(300, description="最大处理时间（秒）")
    generate_report: bool = Field(True, description="是否生成报告")


class ComparisonRequest(BaseModel):
    """比对请求模型"""
    document1_id: str = Field(..., description="第一个文档ID")
    document2_id: str = Field(..., description="第二个文档ID")
    comparison_options: Optional[ComparisonOptions] = Field(
        default_factory=ComparisonOptions,
        description="比对选项"
    )
    priority: int = Field(5, description="任务优先级（1-10）", ge=1, le=10)
    async_processing: bool = Field(True, description="是否异步处理")
    callback_url: Optional[str] = Field(None, description="回调URL")
    tags: Optional[List[str]] = Field(None, description="任务标签")


class ComparisonResponse(BaseModel):
    """比对响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: ComparisonStatus = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    estimated_time: Optional[int] = Field(None, description="预计处理时间（秒）")
    created_at: datetime = Field(..., description="创建时间")
    callback_url: Optional[str] = Field(None, description="回调URL")


class ComparisonResult(BaseModel):
    """比对结果模型"""
    comparison_id: str = Field(..., description="比对ID")
    overall_similarity: float = Field(..., description="整体相似度", ge=0, le=1)
    similarity_level: str = Field(..., description="相似度等级")
    
    # 各项分数
    component_scores: Dict[str, float] = Field(..., description="各组件分数")
    
    # 差异信息
    differences: Dict[str, List[Dict[str, Any]]] = Field(..., description="差异详情")
    
    # 风险评估
    risk_assessment: Dict[str, Any] = Field(..., description="风险评估")
    
    # 建议
    recommendations: List[Dict[str, Any]] = Field(..., description="建议")
    
    # 元数据
    metadata: Dict[str, Any] = Field(..., description="元数据")
    
    # 处理信息
    processing_time: float = Field(..., description="处理时间（秒）")
    timestamp: datetime = Field(..., description="完成时间")


class ComparisonSummary(BaseModel):
    """比对摘要模型"""
    task_id: str = Field(..., description="任务ID")
    overall_similarity: float = Field(..., description="整体相似度")
    similarity_level: str = Field(..., description="相似度等级")
    total_differences: int = Field(..., description="总差异数")
    critical_differences: int = Field(..., description="关键差异数")
    risk_level: str = Field(..., description="风险等级")
    processing_time: float = Field(..., description="处理时间")
    completed_at: datetime = Field(..., description="完成时间")


class ComparisonDetailResponse(BaseModel):
    """比对详情响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: ComparisonStatus = Field(..., description="任务状态")
    progress: Optional[TaskProgress] = Field(None, description="处理进度")
    result: Optional[ComparisonResult] = Field(None, description="比对结果")
    error_message: Optional[str] = Field(None, description="错误消息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")


class ComparisonTask(BaseModel):
    """比对任务模型"""
    id: str = Field(..., description="任务ID")
    document1_id: str = Field(..., description="第一个文档ID")
    document2_id: str = Field(..., description="第二个文档ID")
    status: ComparisonStatus = Field(..., description="任务状态")
    priority: int = Field(..., description="任务优先级")
    options: Optional[ComparisonOptions] = Field(None, description="比对选项")
    progress: Optional[TaskProgress] = Field(None, description="处理进度")
    result_summary: Optional[ComparisonSummary] = Field(None, description="结果摘要")
    error_message: Optional[str] = Field(None, description="错误消息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    tags: Optional[List[str]] = Field(None, description="任务标签")


class ComparisonListResponse(BaseModel):
    """比对任务列表响应模型"""
    tasks: List[ComparisonTask] = Field(..., description="任务列表")
    pagination: PaginationResponse = Field(..., description="分页信息")


class ComparisonStatistics(BaseModel):
    """比对统计模型"""
    total_comparisons: int = Field(..., description="总比对数")
    completed_comparisons: int = Field(..., description="已完成比对数")
    failed_comparisons: int = Field(..., description="失败比对数")
    success_rate: float = Field(..., description="成功率")
    
    # 相似度分布
    similarity_distribution: Dict[str, int] = Field(..., description="相似度分布")
    
    # 平均指标
    average_similarity: float = Field(..., description="平均相似度")
    average_processing_time: float = Field(..., description="平均处理时间")
    
    # 时间范围
    period_start: datetime = Field(..., description="统计开始时间")
    period_end: datetime = Field(..., description="统计结束时间")
    
    # 趋势数据
    daily_counts: List[Dict[str, Any]] = Field(..., description="每日统计")


class DifferenceItem(BaseModel):
    """差异项模型"""
    type: str = Field(..., description="差异类型")
    category: str = Field(..., description="差异类别")
    severity: str = Field(..., description="严重程度")
    description: str = Field(..., description="差异描述")
    location: Optional[Dict[str, Any]] = Field(None, description="位置信息")
    old_value: Optional[str] = Field(None, description="原值")
    new_value: Optional[str] = Field(None, description="新值")
    confidence: Optional[float] = Field(None, description="置信度")


class RiskAssessment(BaseModel):
    """风险评估模型"""
    risk_level: str = Field(..., description="风险等级")
    risk_score: float = Field(..., description="风险分数")
    risk_factors: List[Dict[str, Any]] = Field(..., description="风险因素")
    impact_assessment: Dict[str, Any] = Field(..., description="影响评估")
    mitigation_suggestions: List[str] = Field(..., description="缓解建议")


class QualityAssessment(BaseModel):
    """质量评估模型"""
    overall_quality: float = Field(..., description="整体质量分数")
    quality_level: str = Field(..., description="质量等级")
    quality_metrics: Dict[str, float] = Field(..., description="质量指标")
    quality_issues: List[str] = Field(..., description="质量问题")


class ComparisonConfig(BaseModel):
    """比对配置模型"""
    default_options: ComparisonOptions = Field(..., description="默认选项")
    supported_formats: List[str] = Field(..., description="支持的文件格式")
    max_file_size: int = Field(..., description="最大文件大小")
    max_processing_time: int = Field(..., description="最大处理时间")
    enable_features: Dict[str, bool] = Field(..., description="功能开关")


class ComparisonTemplate(BaseModel):
    """比对模板模型"""
    id: str = Field(..., description="模板ID")
    name: str = Field(..., description="模板名称")
    description: str = Field(..., description="模板描述")
    options: ComparisonOptions = Field(..., description="模板选项")
    is_default: bool = Field(False, description="是否为默认模板")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
