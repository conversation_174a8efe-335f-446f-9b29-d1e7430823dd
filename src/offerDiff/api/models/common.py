"""
通用API数据模型
"""
from pydantic import BaseModel, Field
from typing import Any, Optional, Dict, List
from datetime import datetime


class APIResponse(BaseModel):
    """API响应基础模型"""
    success: bool = Field(..., description="请求是否成功")
    data: Optional[Any] = Field(None, description="响应数据")
    message: str = Field("", description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="响应时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class ErrorResponse(BaseModel):
    """错误响应模型"""
    error: str = Field(..., description="错误类型")
    message: str = Field(..., description="错误消息")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="错误时间")
    request_id: Optional[str] = Field(None, description="请求ID")


class PaginationResponse(BaseModel):
    """分页响应模型"""
    total: int = Field(..., description="总数量")
    limit: int = Field(..., description="每页数量")
    offset: int = Field(..., description="偏移量")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")
    
    @classmethod
    def create(cls, total: int, limit: int, offset: int) -> "PaginationResponse":
        """创建分页响应"""
        return cls(
            total=total,
            limit=limit,
            offset=offset,
            has_next=offset + limit < total,
            has_prev=offset > 0
        )


class ValidationError(BaseModel):
    """验证错误模型"""
    field: str = Field(..., description="字段名")
    message: str = Field(..., description="错误消息")
    value: Optional[Any] = Field(None, description="错误值")


class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field(..., description="状态", regex="^(healthy|degraded|unhealthy)$")
    message: Optional[str] = Field(None, description="状态消息")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="检查时间")


class MetricValue(BaseModel):
    """指标值模型"""
    name: str = Field(..., description="指标名称")
    value: float = Field(..., description="指标值")
    unit: Optional[str] = Field(None, description="单位")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="时间戳")


class TimeSeriesData(BaseModel):
    """时间序列数据模型"""
    timestamp: datetime = Field(..., description="时间戳")
    value: float = Field(..., description="数值")
    label: Optional[str] = Field(None, description="标签")


class ConfigItem(BaseModel):
    """配置项模型"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    description: Optional[str] = Field(None, description="配置描述")
    is_sensitive: bool = Field(False, description="是否敏感信息")


class TaskProgress(BaseModel):
    """任务进度模型"""
    current_step: str = Field(..., description="当前步骤")
    total_steps: int = Field(..., description="总步骤数")
    completed_steps: int = Field(..., description="已完成步骤数")
    progress_percent: float = Field(..., description="进度百分比", ge=0, le=100)
    estimated_remaining_time: Optional[int] = Field(None, description="预计剩余时间（秒）")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")


class FileMetadata(BaseModel):
    """文件元数据模型"""
    filename: str = Field(..., description="文件名")
    file_size: int = Field(..., description="文件大小（字节）")
    file_type: str = Field(..., description="文件类型")
    mime_type: str = Field(..., description="MIME类型")
    checksum: Optional[str] = Field(None, description="文件校验和")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")


class SearchFilter(BaseModel):
    """搜索过滤器模型"""
    field: str = Field(..., description="字段名")
    operator: str = Field(..., description="操作符", regex="^(eq|ne|gt|gte|lt|lte|in|like)$")
    value: Any = Field(..., description="过滤值")


class SortOrder(BaseModel):
    """排序模型"""
    field: str = Field(..., description="排序字段")
    direction: str = Field("asc", description="排序方向", regex="^(asc|desc)$")


class SearchRequest(BaseModel):
    """搜索请求模型"""
    query: Optional[str] = Field(None, description="搜索关键词")
    filters: Optional[List[SearchFilter]] = Field(None, description="过滤条件")
    sort: Optional[List[SortOrder]] = Field(None, description="排序条件")
    limit: int = Field(20, description="返回数量限制", ge=1, le=100)
    offset: int = Field(0, description="偏移量", ge=0)


class BulkOperation(BaseModel):
    """批量操作模型"""
    operation: str = Field(..., description="操作类型")
    target_ids: List[str] = Field(..., description="目标ID列表")
    parameters: Optional[Dict[str, Any]] = Field(None, description="操作参数")


class BulkOperationResult(BaseModel):
    """批量操作结果模型"""
    total: int = Field(..., description="总数量")
    successful: int = Field(..., description="成功数量")
    failed: int = Field(..., description="失败数量")
    errors: Optional[List[Dict[str, Any]]] = Field(None, description="错误详情")
    results: Optional[List[Any]] = Field(None, description="操作结果")


class SystemInfo(BaseModel):
    """系统信息模型"""
    version: str = Field(..., description="系统版本")
    build_time: Optional[datetime] = Field(None, description="构建时间")
    environment: str = Field(..., description="运行环境")
    uptime: int = Field(..., description="运行时间（秒）")
    features: List[str] = Field(default_factory=list, description="功能特性")


class RateLimitInfo(BaseModel):
    """限流信息模型"""
    limit: int = Field(..., description="限制数量")
    remaining: int = Field(..., description="剩余数量")
    reset_time: datetime = Field(..., description="重置时间")
    window_size: int = Field(..., description="时间窗口大小（秒）")


class CacheInfo(BaseModel):
    """缓存信息模型"""
    hit_rate: float = Field(..., description="命中率", ge=0, le=1)
    total_requests: int = Field(..., description="总请求数")
    cache_hits: int = Field(..., description="缓存命中数")
    cache_misses: int = Field(..., description="缓存未命中数")
    cache_size: int = Field(..., description="缓存大小")
    evictions: int = Field(..., description="驱逐次数")


class QueueInfo(BaseModel):
    """队列信息模型"""
    name: str = Field(..., description="队列名称")
    size: int = Field(..., description="队列大小")
    pending: int = Field(..., description="待处理任务数")
    processing: int = Field(..., description="处理中任务数")
    completed: int = Field(..., description="已完成任务数")
    failed: int = Field(..., description="失败任务数")


class DatabaseInfo(BaseModel):
    """数据库信息模型"""
    connection_count: int = Field(..., description="连接数")
    active_connections: int = Field(..., description="活跃连接数")
    idle_connections: int = Field(..., description="空闲连接数")
    total_queries: int = Field(..., description="总查询数")
    slow_queries: int = Field(..., description="慢查询数")
    database_size: Optional[int] = Field(None, description="数据库大小（字节）")
