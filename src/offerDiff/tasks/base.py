"""
任务基类
"""
import time
from typing import Any, Dict, Optional
from celery import Task
from loguru import logger

from src.offerDiff.database.connection import db_manager


class BaseTask(Task):
    """任务基类"""
    
    def __init__(self):
        self.start_time = None
        self.db_session = None
    
    def on_start(self, task_id: str, args: tuple, kwargs: dict):
        """任务开始时的回调"""
        self.start_time = time.time()
        self.db_session = db_manager.get_session()
        logger.info(f"任务 {task_id} 开始执行")
    
    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: dict):
        """任务成功时的回调"""
        execution_time = time.time() - self.start_time if self.start_time else 0
        logger.info(f"任务 {task_id} 执行成功，耗时: {execution_time:.2f}秒")
        
        if self.db_session:
            self.db_session.close()
    
    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo):
        """任务失败时的回调"""
        execution_time = time.time() - self.start_time if self.start_time else 0
        logger.error(f"任务 {task_id} 执行失败，耗时: {execution_time:.2f}秒，错误: {exc}")
        
        if self.db_session:
            self.db_session.rollback()
            self.db_session.close()
    
    def on_retry(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo):
        """任务重试时的回调"""
        logger.warning(f"任务 {task_id} 重试，错误: {exc}")
    
    def update_progress(self, task_id: str, progress: int, message: str = ""):
        """更新任务进度"""
        try:
            # 更新Celery任务状态
            self.update_state(
                task_id=task_id,
                state="PROGRESS",
                meta={
                    "progress": progress,
                    "message": message,
                    "timestamp": time.time()
                }
            )
            
            # 更新数据库中的任务进度
            if self.db_session:
                from src.offerDiff.models.task import TaskModel
                task = self.db_session.query(TaskModel).filter(TaskModel.id == task_id).first()
                if task:
                    task.progress = progress
                    self.db_session.commit()
            
            logger.info(f"任务 {task_id} 进度更新: {progress}% - {message}")
            
        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")
    
    def log_error(self, task_id: str, error: str):
        """记录错误信息"""
        try:
            if self.db_session:
                from src.offerDiff.models.task import TaskModel
                task = self.db_session.query(TaskModel).filter(TaskModel.id == task_id).first()
                if task:
                    task.error_message = error
                    task.status = "failed"
                    self.db_session.commit()
            
            logger.error(f"任务 {task_id} 错误: {error}")
            
        except Exception as e:
            logger.error(f"记录错误信息失败: {e}")
    
    def get_task_config(self) -> Dict[str, Any]:
        """获取任务配置"""
        return {
            "max_retries": 3,
            "default_retry_delay": 60,  # 60秒
            "retry_backoff": True,
            "retry_backoff_max": 600,  # 10分钟
            "retry_jitter": True,
        }
