"""
FastAPI应用创建和配置
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger

from config.settings import settings
from src.offerDiff.offerDiff.api.routes import files, comparison, health
from src.offerDiff.offerDiff.middleware.error_handler import ErrorHandlerMiddleware


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="扫描版PDF报价文件智能比对系统API",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else ["http://localhost:3000"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 添加错误处理中间件
    app.add_middleware(ErrorHandlerMiddleware)
    
    # 注册路由
    app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
    app.include_router(files.router, prefix="/api/v1", tags=["文件管理"])
    app.include_router(comparison.router, prefix="/api/v1", tags=["比对任务"])
    
    # 启动事件
    @app.on_event("startup")
    async def startup_event():
        logger.info("应用启动完成")
        # 这里可以添加数据库连接、缓存初始化等
    
    # 关闭事件
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("应用正在关闭")
        # 这里可以添加资源清理逻辑
    
    # 根路径
    @app.get("/")
    async def root():
        return {
            "message": f"欢迎使用{settings.app_name}",
            "version": settings.app_version,
            "docs": "/docs" if settings.debug else "文档已禁用"
        }
    
    return app
